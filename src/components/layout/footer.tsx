"use client";

import { useEffect, useState } from "react";
import { Logo } from "@/components/ui/logo";
import { Linkedin, X, Youtube } from "lucide-react";
import Link from "next/link";

export function Footer() {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop =
        window.scrollY || document.documentElement.scrollTop || 0;
      const docHeight = document.documentElement.scrollHeight || 0;
      const winHeight = window.innerHeight || 0;
      const maxScrollable = Math.max(1, docHeight - winHeight);
      const progress = Math.min(1, Math.max(0, scrollTop / maxScrollable));
      setScrollProgress(progress);
    };

    handleScroll();
    window.addEventListener("scroll", handleScroll, { passive: true });
    window.addEventListener("resize", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, []);

  const otherItemsStyle = {
    opacity: scrollProgress,
    transform: `translateY(${Math.round((1 - scrollProgress) * 12)}px) scale(${
      0.85 + 0.15 * scrollProgress
    })`,
    transition: "transform 300ms ease, opacity 300ms ease",
    pointerEvents: scrollProgress < 0.05 ? "none" : "auto",
  } as const;

  return (
    <footer className=" bg-[var(--siift-lightest-accent)]/20    border-t-1 border-[var(--siift-light-accent)]">
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col items-center space-y-6">
          {/* Company Info */}
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <Logo
                size={32}
                animated={false}
                showText={true}
                textSize={32}
                href="/"
              />
            </div>
            <p
              className="text-sm text-muted-foreground max-w-md"
              style={otherItemsStyle}
            >
              Streamline your project management with intelligent automation and
              insights.
            </p>
          </div>

          {/* Social Links */}
          <div className="flex space-x-4" style={otherItemsStyle}>
            <Link
              href="https://x.com/siiftAi"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-5 w-5" />
            </Link>
            <Link
              href="https://www.linkedin.com/company/siiftai/posts/?feedView=all"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground"
            >
              <Linkedin className="h-5 w-5" />
            </Link>
            <Link
              href="https://www.youtube.com/channel/UCcm9zM8lMavWGKBEDdupRiw"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground"
            >
              <Youtube className="h-5 w-5" />
            </Link>
          </div>

          {/* Page Links */}
          <div className="flex space-x-6" style={otherItemsStyle}>
            <Link
              href="/about"
              className="text-sm text-muted-foreground hover:text-foreground"
            >
              About
            </Link>
            <Link
              href="/blog"
              className="text-sm text-muted-foreground hover:text-foreground"
            >
              Blog
            </Link>
          </div>

          {/* Copyright */}
          <div
            className="text-sm text-muted-foreground"
            style={otherItemsStyle}
          >
            © {new Date().getFullYear()} siift. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
}
