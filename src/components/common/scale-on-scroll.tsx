"use client";

import React, { useEffect, useRef, useState } from "react";

type ScaleOnScrollProps = {
  children: React.ReactNode;
  fullScale?: number;
  minScale?: number;
  axis?: "y" | "x";
  className?: string;
  style?: React.CSSProperties;
};

function ScaleOnScroll({
  children,
  fullScale = 1,
  minScale = 0.9,
  axis = "y",
  className,
  style,
}: ScaleOnScrollProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState<number>(fullScale);

  useEffect(() => {
    let rafId = 0;

    const updateScale = () => {
      rafId = 0;
      const el = containerRef.current;
      if (!el) return;

      const rect = el.getBoundingClientRect();
      const viewportCenterX = window.innerWidth / 2;
      const viewportCenterY = window.innerHeight / 2;
      const elCenterX = rect.left + rect.width / 2;
      const elCenterY = rect.top + rect.height / 2;

      const distance =
        axis === "y"
          ? Math.abs(elCenterY - viewportCenterY)
          : Math.abs(elCenterX - viewportCenterX);

      const maxDistance =
        axis === "y" ? window.innerHeight * 0.6 : window.innerWidth * 0.6;

      const t = Math.min(1, distance / maxDistance);
      const nextScale = fullScale - (fullScale - minScale) * t;

      setScale(nextScale);
    };

    const handleScrollOrResize = () => {
      if (rafId) return;
      rafId = window.requestAnimationFrame(updateScale);
    };

    updateScale();
    window.addEventListener("scroll", handleScrollOrResize, { passive: true });
    window.addEventListener("resize", handleScrollOrResize);

    return () => {
      if (rafId) cancelAnimationFrame(rafId);
      window.removeEventListener("scroll", handleScrollOrResize);
      window.removeEventListener("resize", handleScrollOrResize);
    };
  }, [axis, fullScale, minScale]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        transform: `scale(${scale})`,
        transition: "transform 120ms ease-out",
        willChange: "transform",
        ...style,
      }}
    >
      {children}
    </div>
  );
}

export default ScaleOnScroll;
export { ScaleOnScroll };
