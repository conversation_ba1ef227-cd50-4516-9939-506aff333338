"use client";

import { JSX, useEffect, useMemo, useRef, useState } from "react";
import { useFocusQueueStore } from "@/stores/focusQueueStore";

interface FocusableProps {
  focusKey: string;
  onAcknowledge?: (key: string) => void;
  children: (opts: { isFocused: boolean }) => JSX.Element;
  // Optional visual/priority level for future use (e.g., stacking, intensity)
  level?: number;
  // When true (default), if any focus is active, non-focused widgets are dimmed and disabled
  dimNonFocusedWhenActive?: boolean;
  // Direction of the sweep effect overlay (default: vertical)
  direction?: "vertical" | "horizontal";
}

export function Focusable({
  focusKey,
  onAcknowledge,
  children,
  level,
  dimNonFocusedWhenActive = true,
  direction = "vertical",
}: FocusableProps) {
  const isFocused = useFocusQueueStore((s) => s.isFocused(focusKey));
  const hasActiveFocus = useFocusQueueStore((s) => Boolean(s.current));
  const acknowledge = useFocusQueueStore((s) => s.acknowledge);

  const [pulse, setPulse] = useState(false);
  const prevFocusedRef = useRef(false);

  // Start a brief pulse animation whenever focus toggles true
  useEffect(() => {
    if (isFocused && !prevFocusedRef.current) {
      setPulse(true);
      const t = setTimeout(() => setPulse(false), 100);
      return () => clearTimeout(t);
    }
    prevFocusedRef.current = isFocused;
  }, [isFocused]);

  const handleClick = () => {
    // If another widget is focused and dimming is enabled, prevent interaction
    if (dimNonFocusedWhenActive && hasActiveFocus && !isFocused) return;
    const key = (focusKey || "").toString();
    if (!key) return;
    acknowledge(key);
    onAcknowledge?.(key);
  };

  // Apply a simple visual emphasis when focused
  const className = useMemo(() => {
    const base = isFocused
      ? "ring-6 ring-[var(--siift-light-accent)] bg-[var(--siift-light-accent)]/15  rounded-lg"
      : "";
    const pulsing = pulse ? "animate-pulse" : "";
    const activeLevel = useFocusQueueStore.getState().current?.level ?? 0;
    const thisLevel = typeof level === "number" ? level : 0;
    const shouldDim =
      ((dimNonFocusedWhenActive && hasActiveFocus && !isFocused) ||
        (hasActiveFocus && !isFocused)) &&
      activeLevel < thisLevel;

    const dimmed = shouldDim
      ? "opacity-10  bg-[var(--muted-foreground)]/20 cursor-not-allowed select-none pointer-events-none rounded-lg"
      : "";

    return [base, pulsing, dimmed].filter(Boolean).join(" ");
  }, [isFocused, pulse, dimNonFocusedWhenActive, hasActiveFocus, level]);

  return (
    <div
      className={[
        className,
        isFocused ? "relative overflow-hidden isolate" : "",
      ]
        .filter(Boolean)
        .join(" ")}
      aria-disabled={
        (dimNonFocusedWhenActive && hasActiveFocus && !isFocused) ||
        (hasActiveFocus &&
          (useFocusQueueStore.getState().current?.level ?? 0) >
            (typeof level === "number" ? level : 0) &&
          !isFocused)
      }
      data-level={typeof level === "number" ? level : undefined}
      onClick={handleClick}
    >
      {children({ isFocused })}
      {isFocused && (
        <div
          aria-hidden
          className="pointer-events-none absolute inset-0 z-[9999]"
        >
          <div className="h-full w-full" style={{ borderRadius: "inherit" }} />
          <style jsx>{`
            .focus-overlay {
              position: absolute;
              inset: 0;
              pointer-events: none;
              will-change: transform;
              border-radius: inherit;
            }
            .focus-overlay.vertical {
              background: linear-gradient(
                to bottom,
                hsl(from var(--siift-mid-accent) h s l / 0.35) 0%,
                hsl(from var(--siift-mid-accent) h s l / 0.35) 25%,
                hsl(from var(--siift-mid-accent) h s l / 0.15) 75%,
                transparent 100%
              );
              transform: translateY(-100%);
              animation: sweepDown 1.5s ease-out forwards;
            }
            .focus-overlay.horizontal {
              background: linear-gradient(
                to right,
                hsl(from var(--siift-mid-accent) h s l / 0.35) 0%,
                hsl(from var(--siift-mid-accent) h s l / 0.35) 25%,
                hsl(from var(--siift-mid-accent) h s l / 0.15) 75%,
                transparent 100%
              );
              transform: translateX(-100%);
              animation: sweepRight 1.5s ease-out forwards;
            }
            @keyframes sweepDown {
              0% {
                transform: translateY(-100%);
              }
              100% {
                transform: translateY(100%);
              }
            }
            @keyframes sweepRight {
              0% {
                transform: translateX(-100%);
              }
              100% {
                transform: translateX(100%);
              }
            }
          `}</style>
          <div
            className={`focus-overlay ${
              direction === "horizontal" ? "horizontal" : "vertical"
            }`}
          />
        </div>
      )}
    </div>
  );
}
