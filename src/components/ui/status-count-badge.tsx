"use client";

import { <PERSON>bul<PERSON>, Zap, Check, AlertTriangle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type BadgeType = "idea" | "action" | "confirmed" | "unproven";

export function StatusCountBadge({
  type,
  count,
  className,
  hideOneCount = true,
  showTooltip = false,
}: {
  type: BadgeType;
  count: number;
  className?: string;
  hideOneCount?: boolean;
  showTooltip?: boolean;
}) {
  if (!count || count <= 0) return null;

  const base =
    "inline-flex items-center gap-1 rounded border px-1.5 py-0.5 h-6 text-[10px]";

  const stylesByType: Record<BadgeType, string> = {
    idea: "bg-yellow-50 dark:bg-yellow-100 text-black dark:text-black text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6",
    action:
      "bg-blue-100 dark:bg-blue-100 text-blue-800 dark:text-black text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6",
    confirmed:
      "bg-green-100 dark:bg-green-100 text-green-900 dark:text-black text-xs px-1.5 border border-green-700 dark:border-green-600 h-6",
    unproven:
      "bg-red-50 dark:bg-red-50 text-red-300 dark:text-black text-xs px-1.5 border border-red-100 dark:border-red-200 h-6",
  };

  const Icon =
    type === "idea"
      ? Lightbulb
      : type === "action"
      ? Zap
      : type === "confirmed"
      ? Check
      : AlertTriangle;

  const labelByType: Record<BadgeType, string> = {
    idea: "Ideas suggested for this item",
    action: "Actionable suggestions",
    confirmed: "Confirmed decisions",
    unproven: "Unproven decisions",
  };

  const badge = (
    <span
      className={`${base} ${stylesByType[type]} ${className || ""}`}
      aria-label={labelByType[type]}
    >
      <Icon className="h-3 w-3 text-red" />
      {hideOneCount && count === 1 ? "" : count}
    </span>
  );

  if (!showTooltip) return badge;

  return (
    <Tooltip>
      <TooltipTrigger asChild>{badge}</TooltipTrigger>
      <TooltipContent sideOffset={4}>{labelByType[type]}</TooltipContent>
    </Tooltip>
  );
}
