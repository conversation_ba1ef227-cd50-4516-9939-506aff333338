"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { ProjectInputSection } from "@/components/shared/ProjectInputSection";
import { DottedBackground } from "@/components/ui/dotted-background";
import Link from "next/link";
import { Logo } from "@/components/ui/logo";
import { Home } from "lucide-react";
import { Separator } from "@radix-ui/react-dropdown-menu";
import { IButton } from "../ui/ibutton";

const ALLOWED_SHA256_HEX = new Set<string>([
  "f4340be4842ad8dc1b863a3386dcf0015abc063f60c76e495421407768a7e31f",
  "d1d6858aafb43634d2758632fa5d4bd9c4a4a54cab0989d4f788c0871a1d0046",
  "4376c64a6adc8d0e6a64e0b24b2e9efb29ce5e62c42a6fa71ed855df56a470d5",
  "4efba11ebae3d211946b5e12d61909a64753a0a5f6d76995f0664e0db3a6a039",
  "ff8733135cfcdce47704a8053776b850f6c46c8bd754c7e0aebc07bee57daf75",
]);

function bytesToHexString(bytes: ArrayBuffer): string {
  const byteArray = new Uint8Array(bytes);
  const hexCodes: string[] = [];
  for (let i = 0; i < byteArray.length; i += 1) {
    const hexCode = byteArray[i].toString(16).padStart(2, "0");
    hexCodes.push(hexCode);
  }
  return hexCodes.join("");
}

async function sha256Hex(input: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  return bytesToHexString(hashBuffer);
}

interface BetaAccessGateProps {
  mode?: "landing" | "auth";
  onUnlock?: () => void;
}

export function BetaAccessGate({
  mode = "landing",
  onUnlock,
}: BetaAccessGateProps) {
  const router = useRouter();
  const [password, setPassword] = useState<string>("");
  const [isVerifying, setIsVerifying] = useState<boolean>(false);
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [inviteEmail, setInviteEmail] = useState<string>("");
  const [isRequestingInvite, setIsRequestingInvite] = useState<boolean>(false);

  useEffect(() => {
    const cookies = document.cookie.split("; ");
    const betaCookie = cookies.find((c) => c.startsWith("beta_access="));
    const betaValue = betaCookie?.split("=")[1];
    setHasAccess(betaValue === "1");
  }, []);

  const canSubmit = useMemo(
    () => password.trim().length > 0 && !isVerifying,
    [password, isVerifying]
  );

  const canSubmitInvite = useMemo(() => {
    if (isRequestingInvite) return false;
    const email = inviteEmail.trim();
    if (!email) return false;
    // Simple email validation
    return /.+@.+\..+/.test(email);
  }, [inviteEmail, isRequestingInvite]);

  const setBetaCookieAndRedirect = useCallback(() => {
    const maxAgeDays = 7;
    const maxAgeSeconds = maxAgeDays * 24 * 60 * 60;
    const cookie = `beta_access=1; Path=/; Max-Age=${maxAgeSeconds}; SameSite=Lax`;
    // Avoid always setting Secure so it also works on localhost during development
    document.cookie = cookie;
    if (mode === "auth") {
      if (onUnlock) onUnlock();
      setHasAccess(true);
    } else {
      router.push("/auth/login");
    }
  }, [router, mode, onUnlock]);

  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      if (!canSubmit) return;
      try {
        setIsVerifying(true);
        const hash = await sha256Hex(password.trim());
        if (ALLOWED_SHA256_HEX.has(hash)) {
          setBetaCookieAndRedirect();
        } else {
          toast.error("Invalid access code. Please try again.");
        }
      } catch (err) {
        console.error("Beta access verification failed:", err);
        toast.error("Something went wrong. Please try again.");
      } finally {
        setIsVerifying(false);
      }
    },
    [canSubmit, password, setBetaCookieAndRedirect]
  );

  const handleInviteSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      if (!canSubmitInvite) return;
      try {
        setIsRequestingInvite(true);
        const res = await fetch("/api/waitlist", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            email: inviteEmail.trim(),
            source: "beta-access-gate",
          }),
        });
        const data = await res.json();
        if (!res.ok || !data?.success) {
          throw new Error(data?.error || "Failed to submit request");
        }
        toast.success("Request sent! We'll be in touch soon.");
        setInviteEmail("");
      } catch (err) {
        console.error("Early access request failed:", err);
        toast.error("Could not send request. Please try again later.");
      } finally {
        setIsRequestingInvite(false);
      }
    },
    [canSubmitInvite, inviteEmail]
  );

  if (hasAccess) {
    if (mode === "auth") return null;
    return (
      <ProjectInputSection variant="landing" showFeatureBadges shrinkOnScroll />
    );
  }

  return (
    <div className="relative min-h-screen w-full overflow-hidden flex items-center justify-center">
      {/* Dotted Background Pattern */}
      <DottedBackground
        fadeEdge={20}
        dotSizes={[1, 1.5, 2]}
        spacing={25}
        dotsPerRow={8}
        opacity={0.02}
        darkOpacity={0.15}
        className="pointer-events-none"
      />

      {/* Content */}
      <div className=" max-w-5xl  items-center justify-center flex flex-col gap-6 px-4">
        <div className="pt-8">
          <Logo size={40} animated={true} href="/" />
        </div>

        <div className="border-2 border-[var(--siift-mid-accent)]/90 rounded-2xl  ">
          {/* Right: Beta access gate */}
          <div className="justify-self-center w-full max-w-sm   rounded-2xl">
            <form
              onSubmit={handleSubmit}
              className="group relative  rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden transition-all duration-300   bg-[var(--siift-light-accent)]/30 border-b-2 border-r-2 border-l-2 border-[var(--siift-light-accent)]/30"
            >
              <div className="relative z-10">
                <div className="text-center space-y-2 mb-4">
                  <h2 className="text-xl font-semibold">
                    Beta access required
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    siift is currently in a private beta. If you received an
                    access code, enter it below to continue.
                  </p>
                </div>

                <div className="space-y-3">
                  <Input
                    type="password"
                    autoComplete="off"
                    placeholder="Enter access code"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full bg-background/50 backdrop-blur focus:ring-[var(--primary)]/20 focus:border-[var(--primary)] transition-all duration-300 hover:shadow-lg"
                  />
                  <Button
                    type="submit"
                    disabled={!canSubmit}
                    className="w-full bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)] shadow-lg transition-all duration-300"
                  >
                    {isVerifying ? "Verifying..." : "Unlock beta"}
                  </Button>
                </div>

                <div className="mt-3 text-xs text-muted-foreground text-center">
                  Your access is stored for 7 days on this device.
                </div>
              </div>
            </form>

            <div className="relative flex justify-center text-xs uppercase mt-3">
              <span className=" text-muted-foreground  ">
                Don't have an access code?
              </span>
            </div>
            <form
              onSubmit={handleInviteSubmit}
              className="group relative    rounded-2xl p-6  duration-300 "
            >
              <div className="relative z-10">
                <div className="space-y-2 mb-2">
                  <p className="text-sm text-muted-foreground">
                    We’re onboarding gradually. Join the early access list and
                    we’ll reach out as soon as new seats are available.
                  </p>
                </div>
                <div className="flex flex-col gap-2">
                  <Input
                    type="email"
                    autoComplete="off"
                    placeholder="<EMAIL>"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    className="w-full bg-background/50 backdrop-blur focus:ring-[var(--primary)]/20 focus:border-[var(--primary)] transition-all duration-300 hover:shadow-lg"
                  />
                  <IButton
                    type="submit"
                    variant="outline"
                    disabled={!canSubmitInvite}
                    className="w-full"
                  >
                    {isRequestingInvite ? "Sending..." : "Request invite"}
                  </IButton>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div className="text-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            <Home className="w-4 h-4" />
            Back to home
          </Link>
        </div>
      </div>
      {/* Home link - Outside the card */}
    </div>
  );
}

export default BetaAccessGate;
