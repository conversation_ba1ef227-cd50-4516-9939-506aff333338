"use client";

import { Message<PERSON>quare, Send, Star } from "lucide-react";
import { useEffect, useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Sheet,
  She<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { IButton } from "../ui/ibutton";
import { Logo } from "@/components/ui/logo";

type FeedbackFABProps = {
  projectId: string;
};

export function FeedbackFAB({ projectId }: FeedbackFABProps) {
  const [open, setOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [form, setForm] = useState({
    message: "",
    review: "",
    hypotheticalPay: "",
    isReview: false,
    displayName: "",
    email: "",
  });

  // Periodic attention pulse: swap icon for animated logo briefly
  const [attentionActive, setAttentionActive] = useState(false);
  const attentionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Run every 10s; show for ~2s
    const ATTENTION_INTERVAL_MS = 5000;
    const ATTENTION_DURATION_MS = 2000;

    // Clear any existing timers before setting new ones
    if (intervalRef.current) clearInterval(intervalRef.current);
    if (attentionTimeoutRef.current) clearTimeout(attentionTimeoutRef.current);

    intervalRef.current = setInterval(() => {
      // Only pulse when the sheet is closed
      if (open) return;
      setAttentionActive(true);
      if (attentionTimeoutRef.current)
        clearTimeout(attentionTimeoutRef.current);
      attentionTimeoutRef.current = setTimeout(() => {
        setAttentionActive(false);
      }, ATTENTION_DURATION_MS);
    }, ATTENTION_INTERVAL_MS);

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (attentionTimeoutRef.current)
        clearTimeout(attentionTimeoutRef.current);
    };
  }, [open]);

  const reset = () => {
    setForm({
      message: "",
      review: "",
      hypotheticalPay: "",
      isReview: false,
      displayName: "",
      email: "",
    });
    setError(null);
    setSuccess(false);
  };

  const isFormValid = () => {
    if (!form.message.trim()) return false;
    if (!form.review.trim()) return false;
    if (!form.displayName.trim()) return false;
    if (!form.email.trim()) return false;
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.message.trim()) {
      setError("Please enter your feedback.");
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(false);
    try {
      const combinedMessage = `Feedback:\n${form.message}\n\nReview:\n${
        form.review
      }\n\nHypothetical payment:\n${form.hypotheticalPay || "-"}`;
      const payload = {
        projectId,
        message: combinedMessage,
        testimonial: form.isReview,
        displayName: form.displayName || undefined,
        email: form.email || undefined,
        allowPublic: form.isReview,
        source: "project",
      } as const;

      const res = await fetch("/api/feedback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const json = await res.json();
      if (res.ok && json?.success) {
        setSuccess(true);
        reset();
        setOpen(false);
      } else {
        throw new Error(json?.error || "Failed to send feedback");
      }
    } catch (err: any) {
      setError(err?.message || "Failed to send feedback");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        if (!v) reset();
        setOpen(v);
      }}
    >
      <Tooltip>
        <TooltipTrigger asChild>
          <SheetTrigger asChild>
            <Button
              size="lg"
              className="fixed bottom-6 right-6 z-50 h-18 w-18 p-0 rounded-full border border-[var(--siift-bold-accent)] bg-[var(--siift-light-accent)] text-[var(--foreground)] hover:bg-[var(--siift-bold-accent)] shadow-lg hover:shadow-xl"
              aria-label="Send feedback"
            >
              <span className="relative inline-flex items-center justify-center">
                {/* Default icon */}
                <span
                  className={
                    (attentionActive ? "opacity-0" : "opacity-100") +
                    " transition-opacity duration-500"
                  }
                >
                  <MessageSquare className="size-6" />
                </span>
                <span
                  className={
                    (attentionActive ? "opacity-100" : "opacity-0") +
                    " absolute inset-0 flex items-center justify-center transition-opacity duration-500"
                  }
                >
                  <Star className="size-6" />
                </span>
              </span>
            </Button>
          </SheetTrigger>
        </TooltipTrigger>
        <TooltipContent sideOffset={8}>Send feedback</TooltipContent>
      </Tooltip>

      <SheetContent side="bottom" className="max-w-3xl mx-auto rounded-t-xl">
        <SheetHeader>
          <SheetTitle>Share your feedback</SheetTitle>
        </SheetHeader>

        <form onSubmit={handleSubmit} className="px-4 pb-4 space-y-4">
          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Feedback
            </label>
            <Textarea
              placeholder="Tell us what's working, what's not, or what you'd like to see…"
              value={form.message}
              onChange={(e) =>
                setForm((f) => ({ ...f, message: e.target.value }))
              }
              className="min-h-[120px] text-md"
              required
            />
          </div>

          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Review <span className="text-[var(--destructive)]">*</span>
            </label>
            <Textarea
              placeholder={
                "We’d really appreciate a quick review sharing the value you see, or any other praise you may have 🙏"
              }
              value={form.review}
              onChange={(e) =>
                setForm((f) => ({ ...f, review: e.target.value }))
              }
              className="min-h-[100px] text-md"
              required
            />
          </div>

          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Hypothetically, would you actually pay for this? Why or why not?
            </label>
            <Textarea
              placeholder="Be honest – your input helps us prioritize the right things."
              value={form.hypotheticalPay}
              onChange={(e) =>
                setForm((f) => ({ ...f, hypotheticalPay: e.target.value }))
              }
              className="min-h-[80px] text-md"
            />
          </div>

          <div
            className="flex items-start gap-3 rounded-md border border-[var(--border)] p-3 cursor-pointer hover:bg-[var(--muted)]/50 transition-colors"
            onClick={() => setForm((f) => ({ ...f, isReview: !f.isReview }))}
          >
            <input
              id="isReview"
              type="checkbox"
              checked={form.isReview}
              onChange={(e) =>
                setForm((f) => ({ ...f, isReview: e.target.checked }))
              }
              className="mt-1 h-4 w-4 accent-[var(--primary)] cursor-pointer"
            />
            <label
              htmlFor="isReview"
              className="text-md leading-6 cursor-pointer flex-1"
            >
              I consent to my review being shared publicly by siift
            </label>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <label className="text-md text-[var(--muted-foreground)]">
                Display name{" "}
                <span className="text-[var(--destructive)]">*</span>
              </label>
              <Input
                placeholder="Jane Doe"
                value={form.displayName}
                onChange={(e) =>
                  setForm((f) => ({ ...f, displayName: e.target.value }))
                }
                required
                className={
                  !form.displayName.trim()
                    ? "border-[var(--destructive)] text-md"
                    : "text-md"
                }
              />
            </div>
            <div className="grid gap-2">
              <label className="text-md text-[var(--muted-foreground)]">
                Email <span className="text-[var(--destructive)]">*</span>
              </label>
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={form.email}
                className={
                  !form.email.trim()
                    ? "border-[var(--destructive)] text-md"
                    : "text-md"
                }
                required
                onChange={(e) =>
                  setForm((f) => ({ ...f, email: e.target.value }))
                }
              />
            </div>
          </div>

          {error && (
            <div className="text-[var(--destructive)] text-sm">{error}</div>
          )}

          {success && (
            <div className="text-[var(--primary)] text-sm">
              Thanks for your feedback!
            </div>
          )}

          <SheetFooter className="p-0">
            <div className="ml-auto flex items-center gap-2">
              <SheetClose asChild>
                <IButton
                  variant="outline"
                  type="button"
                  size="md"
                  className="text-sm"
                >
                  Cancel
                </IButton>
              </SheetClose>
              <IButton
                type="submit"
                hoverColor="grey"
                hoverScale={true}
                trailing={<Send className="size-4" />}
                size="md"
                className="bg-[var(--primary)]/10   "
                showBorder={true}
                disabled={submitting || !isFormValid()}
              >
                {submitting ? "Sending…" : "Send feedback"}
              </IButton>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}

export default FeedbackFAB;
