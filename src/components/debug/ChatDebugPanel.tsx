"use client";

import { useMemo, useState } from "react";
import { useAiDebugStore } from "@/stores/aiDebugStore";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

type ChatDebugPanelProps = {
  projectId: string;
};

export function ChatDebugPanel({ projectId }: ChatDebugPanelProps) {
  const [expanded, setExpanded] = useState<boolean>(false);
  const [filter, setFilter] = useState<string>("");
  const events = useAiDebugStore((s) => s.getEvents(projectId));
  const clear = useAiDebugStore((s) => s.clearEvents);

  const filtered = useMemo(() => {
    if (!filter.trim()) return events;
    const needle = filter.trim().toLowerCase();
    return events.filter(
      (e) =>
        e.name.toLowerCase().includes(needle) ||
        JSON.stringify(e.payload || {})
          .toLowerCase()
          .includes(needle)
    );
  }, [events, filter]);

  return (
    <Card className="border-dashed">
      <CardHeader className="py-2 px-3">
        <div className="flex items-center justify-between gap-2">
          <CardTitle className="text-sm">AI Debug Events</CardTitle>
          <div className="flex items-center gap-2">
            <input
              placeholder="Filter..."
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="h-7 px-2 rounded border text-xs bg-background"
            />
            <Button
              size="sm"
              variant="outline"
              className="h-7 text-xs"
              onClick={() => clear(projectId)}
            >
              Clear
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-7 text-xs"
              onClick={() => setExpanded((v) => !v)}
            >
              {expanded ? "Hide" : "Show"}
            </Button>
          </div>
        </div>
      </CardHeader>
      {expanded && (
        <CardContent className="px-3 pb-3">
          <div className="max-h-40 overflow-auto text-xs space-y-1 font-mono">
            {filtered.length === 0 && (
              <div className="text-muted-foreground">No events yet.</div>
            )}
            {filtered
              .slice()
              .reverse()
              .map((e) => (
                <div key={e.id} className="border-b border-border/40 pb-1">
                  <div className="flex items-center gap-2">
                    <span className="text-[10px] text-muted-foreground">
                      {new Date(e.timestamp).toLocaleTimeString()}
                    </span>
                    <span className="px-1 rounded bg-muted text-foreground">
                      {e.name}
                    </span>
                  </div>
                  <pre className="whitespace-pre-wrap break-words">
                    {formatEventPayload(e.name, e.payload)}
                  </pre>
                </div>
              ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}

function safeStringify(value: unknown): string {
  try {
    return JSON.stringify(value ?? {}, null, 2);
  } catch {
    return String(value ?? "");
  }
}

function formatEventPayload(name: string, payload: any): string {
  try {
    // For token_stream, print each token on its own line for readability
    if (name === "token_stream" && Array.isArray(payload?.tokens)) {
      const tokens = payload.tokens
        .filter((t: unknown) => typeof t === "string")
        .map((t: string) => t)
        .join("\n");
      return tokens || "";
    }
    // For token event, prefer `content` then `token`
    if (name === "token") {
      const text = payload?.content ?? payload?.token;
      if (typeof text === "string") return text;
    }
  } catch {}
  return safeStringify(payload);
}

export default ChatDebugPanel;
