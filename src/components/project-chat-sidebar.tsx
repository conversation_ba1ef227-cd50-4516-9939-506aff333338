"use client";

import { AiIntakeProgress } from "@/components/ai/AiIntakeProgress";
import { Button } from "@/components/ui/button";
import { useSidebar } from "@/components/ui/sidebar";
import { useAiChat } from "@/hooks/useAiChat";
import { useAiIntake } from "@/hooks/useAiIntake";
import { safeLocalStorage } from "@/lib/storage";
import {
  generateOnboardingMessageId,
  generateWelcomeMessageId,
  deduplicateMessages,
} from "@/lib/messageId";
import { useChatStore } from "@/stores/chatStore";
import { useFocusQueueStore } from "@/stores/focusQueueStore";
import type { ChatMessage } from "@/types/Chat.types";
import { useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { AI_Prompt } from "./ui/animated-ai-input";
import { ChatBubble, ChatBubbleMessage } from "./ui/chat-bubble";
import { ChatMessageList } from "./ui/chat-message-list";
import { Logo } from "./ui/logo";
import { ChatDebugPanel } from "@/components/debug/ChatDebugPanel";

interface ProjectChatSidebarProps {
  projectId: string;
  isCollapsed?: boolean;
  onCollapseChange?: (collapsed: boolean) => void;
  embedded?: boolean; // New prop to indicate if it's embedded in sidebar
  chatWidth?: "45%" | "45%";
  setChatWidth?: (width: "45%" | "45%") => void;
  isChatCollapsed?: boolean;
  setIsChatCollapsed?: (collapsed: boolean) => void;
  selectedBusinessItem?: any;
  projectTitle?: string; // Add project title prop
}

// Welcome message based on context
const getWelcomeMessage = (
  isDetailsPage: boolean = false,
  topicName?: string
): ChatMessage => ({
  id: generateWelcomeMessageId(),
  user: "siift AI",
  avatar: "",
  message:
    isDetailsPage && topicName
      ? `So far, we have a few ideas for ${topicName} in the focus table on the right - let's validate them with by setting some actions and then recording the results!`
      : "", // : "Ok we've filled in a few key topics, click on one of them to dive in!",
  timestamp: new Date().toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  }),
  isCurrentUser: false,
});

export function ProjectChatSidebar({
  projectId,
  embedded = false,
  selectedBusinessItem,
  projectTitle,
}: ProjectChatSidebarProps) {
  const { state } = useSidebar();
  const enqueueFocus = useFocusQueueStore((s) => s.enqueue);

  // Chat store integration
  const chatStore = useChatStore();
  const messages = chatStore.messages;

  // AI chat hook for streaming
  const topicName = selectedBusinessItem?.title as string | undefined;
  const { sendMessage, cancelMessage, isStreaming, isLoading } = useAiChat(
    projectId,
    {
      topicName,
    }
  );
  const { stage } = useAiIntake();
  const [showIntakeProgress, setShowIntakeProgress] = useState(false);

  // Auto-show intake progress when stage is active
  useEffect(() => {
    const isIntakeActive = stage && !["idle", "ready"].includes(stage);
    setShowIntakeProgress(isIntakeActive);
  }, [stage]);

  // Initialize chat scope and load messages - separate concerns for stability
  useEffect(() => {
    if (!projectId) return;

    // Switch to the proper scope per project/topic
    chatStore.setScope?.(projectId, topicName ?? null);
  }, [projectId, topicName]);

  // Initialize messages once per scope - avoid reloading on every focus change
  useEffect(() => {
    if (!projectId) return;

    const existingMessages = chatStore.messages;

    // If we already have messages for this scope, don't reload
    if (existingMessages.length > 0) return;

    const currentScopeKey = chatStore.getScopeKey?.(
      projectId,
      topicName ?? null
    );

    // Load from scoped localStorage
    const lsKey =
      currentScopeKey && currentScopeKey.includes("|t:")
        ? `chat_messages_${projectId}_topic_${currentScopeKey.split("|t:")[1]}`
        : `chat_messages_${projectId}`;
    const savedMessages = safeLocalStorage.getJSON<ChatMessage[]>(lsKey, []);

    // Get welcome message
    const welcomeMessage = getWelcomeMessage(
      !!selectedBusinessItem,
      selectedBusinessItem?.title
    );

    let allMessages = [welcomeMessage];

    // Only show onboarding messages in main chat (when no business item is selected)
    if (!selectedBusinessItem) {
      // Create onboarding messages only for main chat
      const ONBOARDING_MESSAGES: string[] = [
        // "Welcome to your project workspace!",
        // 'The "siift-table" on the right is where all your business information will be organized and acted upon, step by step.',
        // "As you ideate, iterate and validate topics, more will unlock and your progress bar will increase.",
        // "Our bots are currently researching your idea on the web, to get a big picture view of the opportunity - this may take a minute!",
        // "Once the first round of research is done, you'll see the first few topics fill in",
      ];

      // we shouldn't use hardcoded messages here, we should use the ONBOARDING_MESSAGES from useAiIntake
      const onboardingMessages: ChatMessage[] = ONBOARDING_MESSAGES.map(
        (message) => ({
          id: generateOnboardingMessageId(),
          user: "siift AI",
          avatar: "",
          message,
          timestamp: new Date().toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
          isCurrentUser: false,
          isOnboarding: true,
        })
      );

      allMessages = [welcomeMessage, ...onboardingMessages, ...savedMessages];
    } else {
      // Focus chat: just welcome message + saved messages (no onboarding)
      allMessages = [welcomeMessage, ...savedMessages];
    }

    // Set messages in store (the store will ensure proper ordering)
    chatStore.setMessages(allMessages);
  }, [projectId, topicName, selectedBusinessItem?.title]);

  // One-time table focus nudge
  useEffect(() => {
    if (!projectId) return;

    const timer = setTimeout(() => {
      enqueueFocus("siftt", 3000, 5);
    }, 300);

    return () => clearTimeout(timer);
  }, [projectId]);

  // Handle sending messages - now using the useAiChat hook
  const handleSendMessage = sendMessage;
  // Use external state if provided, otherwise use internal state

  // Chat is always expanded - no collapse functionality

  // Render embedded version for sidebar
  if (embedded) {
    // Don't show anything when sidebar is collapsed
    if (state === "collapsed") {
      return null;
    }

    // Always show expanded chat - no collapse functionality
    return (
      <div
        className={`w-full h-full border-t border-[var(--siift-light-mid)]/50 ${
          selectedBusinessItem
            ? "bg-[var(--siift-lightest-accent)]/40 dark:bg-[color:var(--siift-dark-accent)]/20"
            : "bg-[var(--siift-light-accent)]/10 dark:bg-[color:var(--siift-darker)]/40"
        } backdrop-blur-sm flex flex-col transition-all duration-300`}
      >
        {/* AI Intake Progress - dev only */}
        {process.env.NODE_ENV === "development" && (
          <div className="p-3 border-b border-border/50">
            {/* {showIntakeProgress && <AiIntakeProgress projectId={projectId} />} */}
            <ChatDebugPanel projectId={projectId} />
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-hidden w-full min-h-0 relative">
          <ChatMessageList className="w-full h-full">
            {(() => {
              // Deduplicate messages by ID to ensure uniqueness
              const uniqueMessages = deduplicateMessages(messages);

              // Filter out empty AI messages that are not the last message or currently loading
              const filteredMessages = uniqueMessages.filter((msg, index) => {
                // Keep all user messages
                if (msg.isCurrentUser) return true;

                // Keep all non-empty AI messages
                if (msg.message.trim().length > 0) return true;

                // For empty AI messages, only keep if it's the last message AND currently loading/streaming
                const isLastMessage = index === uniqueMessages.length - 1;
                const isCurrentlyGenerating =
                  isLastMessage && (isLoading || isStreaming);
                return isCurrentlyGenerating;
              });

              // Move console.log outside of render to prevent infinite loops
              if (typeof window !== "undefined") {
                setTimeout(() => {
                  console.log("[ChatComponent] Rendering messages:", {
                    totalMessages: filteredMessages.length,
                    lastFewMessages: filteredMessages.slice(-3).map((m) => ({
                      id: m.id,
                      isUser: m.isCurrentUser,
                      content: m.message.slice(0, 40) + "...",
                      length: m.message.length,
                    })),
                  });
                }, 0);
              }
              return filteredMessages;
            })().map((msg) => (
              <ChatBubble
                key={msg.id}
                className="mt-2"
                variant={msg.isCurrentUser ? "sent" : "received"}
              >
                {!msg.isCurrentUser && (
                  <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Logo
                      size={20}
                      animated={
                        isStreaming &&
                        msg.id === (messages[messages.length - 1]?.id || "")
                      }
                    />
                  </div>
                )}
                <ChatBubbleMessage
                  variant={msg.isCurrentUser ? "sent" : "received"}
                  isLoading={
                    !msg.isCurrentUser &&
                    msg.id === (messages[messages.length - 1]?.id || "") &&
                    (isLoading || isStreaming) &&
                    ((msg.message?.trim?.().length ?? 0) === 0 ||
                      msg.message.startsWith("Sifting...") ||
                      msg.message.startsWith("Starting your project setup"))
                  }
                >
                  <div className="text-md leading-relaxed">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        // Custom styling for markdown elements
                        p: ({ children }) => (
                          <p className="mb-2 last:mb-0">{children}</p>
                        ),
                        ul: ({ children }) => (
                          <ul className="mb-2 last:mb-0 ml-4 list-disc space-y-1">
                            {children}
                          </ul>
                        ),
                        ol: ({ children }) => (
                          <ol className="mb-2 last:mb-0 ml-4 list-decimal space-y-1">
                            {children}
                          </ol>
                        ),
                        li: ({ children }) => (
                          <li className="leading-relaxed">{children}</li>
                        ),
                        strong: ({ children }) => (
                          <strong className="font-semibold">{children}</strong>
                        ),
                        em: ({ children }) => (
                          <em className="italic">{children}</em>
                        ),
                        code: ({ children }) => (
                          <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
                            {children}
                          </code>
                        ),
                        pre: ({ children }) => (
                          <pre className="bg-muted p-3 rounded-md overflow-x-auto my-2">
                            {children}
                          </pre>
                        ),
                        blockquote: ({ children }) => (
                          <blockquote className="border-l-4 border-primary/20 pl-4 italic text-muted-foreground my-2">
                            {children}
                          </blockquote>
                        ),
                        h1: ({ children }) => (
                          <h1 className="text-lg font-bold mb-2 mt-4 first:mt-0">
                            {children}
                          </h1>
                        ),
                        h2: ({ children }) => (
                          <h2 className="text-md font-semibold mb-2 mt-3 first:mt-0">
                            {children}
                          </h2>
                        ),
                        h3: ({ children }) => (
                          <h3 className="text-sm font-medium mb-1 mt-2 first:mt-0">
                            {children}
                          </h3>
                        ),
                      }}
                    >
                      {msg.message.trim()}
                    </ReactMarkdown>
                  </div>
                  {!msg.isCurrentUser && msg.cta?.type === "refetch_topics" && (
                    <div className="mt-2">
                      <Button
                        size="sm"
                        className="h-7"
                        onClick={async () => {
                          // Aggressively invalidate and refetch topics and entries using our shared client
                          const { queryClient } = await import(
                            "@/lib/queryClient"
                          );
                          const keys: any[] = [
                            ["all-project-topics-v2", projectId],
                            ["all-topic-entries", projectId],
                            ["topics", projectId],
                            ["business-sections", projectId],
                          ];
                          keys.forEach((k) =>
                            queryClient.invalidateQueries({ queryKey: k })
                          );
                          keys.forEach((k) =>
                            queryClient.refetchQueries({ queryKey: k })
                          );
                        }}
                      >
                        {msg.cta.label || "Start siifting"}
                      </Button>
                    </div>
                  )}
                </ChatBubbleMessage>
              </ChatBubble>
            ))}
          </ChatMessageList>
        </div>

        {/* Message Input */}
        <div className="p-3 border-t border-border/50 bg-muted/30 backdrop-blur-sm w-full flex-shrink-0">
          <AI_Prompt
            onSendMessage={handleSendMessage}
            onStop={() => cancelMessage()}
            isLoading={isLoading || isStreaming}
            placeholder={
              isLoading || isStreaming
                ? "AI is responding..."
                : selectedBusinessItem
                ? `Ask me about ${selectedBusinessItem.title}...`
                : projectTitle
                ? `Ask me anything about ${projectTitle}...`
                : "Ask me anything about your project..."
            }
          />

          {/* Disclaimer */}
          <div className="mt-1 px-2">
            <p className="text-[10px] text-center opacity-50 leading-tight">
              siift can make mistakes. Please double check answers to ensure
              accuracy.
            </p>
          </div>
        </div>
      </div>
    );
  }
}
