"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";

import { useActionsStore } from "@/stores/actionsStore";
import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import {
  Circle,
  CheckCircle,
  Clock,
  AlertCircle,
  ArrowUpRight,
  Info,
} from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import type { ReactElement } from "react";
import { useQueryClient } from "@tanstack/react-query";
import {
  useProjectActionItems,
  useInvalidateSiift,
} from "@/hooks/queries/useSiift";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { IButton } from "../../ui/ibutton";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

// Action types
type ActionType = "manual" | "deep-research" | "web-search" | "dataset-search";

// Progress status types
type ProgressStatus = "idea" | "action" | "result" | "confirmed";

// Result object structure
interface ActionResult {
  id: string;
  content: string;
  timestamp: string;
  type: "text" | "link" | "file";
}

// Action object structure
interface ActionItem {
  id: string;
  type: ActionType;
  content: string;
  credit: number;
  progress: number; // 0-100 percentage
  status: ProgressStatus;
  results: ActionResult[];
}

// Parse actions from JSON string or create default structure
function parseActions(actionsString: string): ActionItem[] {
  if (!actionsString?.trim()) return [];

  try {
    const parsed = JSON.parse(actionsString);
    if (Array.isArray(parsed)) {
      return parsed.map((item) => ({
        // Use a deterministic id when missing to keep UI overrides stable between renders
        id: String(
          item.id ??
            item._id ??
            `${item.type || "manual"}|${item.content || ""}|${item.credit ?? 0}`
        ),
        type: item.type || "manual",
        content: item.content || "",
        credit: item.credit || 1,
        progress: item.progress || 0,
        status: item.status || "idea",
        results: item.results || [],
      }));
    }
  } catch (e) {
    // If not valid JSON, treat as legacy text format
    return actionsString
      .split("\n")
      .filter((item) => item.trim())
      .map((item) => ({
        // Stable id based on content for legacy entries
        id: `legacy|${item.trim()}`,
        type: "manual" as ActionType,
        content: item.trim(),
        credit: 1,
        progress: 0,
        status: "idea" as ProgressStatus,
        results: [],
      }));
  }

  return [];
}

// Convert actions back to JSON string for storage
function actionsToJson(actions: ActionItem[]): string {
  return JSON.stringify(actions);
}

interface ActionsCellProps {
  detail: BusinessItemDetail;
  disabled?: boolean;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
}

export function ActionsCell({ detail, disabled, onSave }: ActionsCellProps) {
  // Progress management state

  // Progress management state
  const [progressOpen, setProgressOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<ActionItem | null>(null);
  const [actionSheetOpen, setActionSheetOpen] = useState(false);
  const [sidebarActionDraft, setSidebarActionDraft] = useState<string>("");
  const [sidebarResultDraft, setSidebarResultDraft] = useState<string>("");
  const sidebarSavedRef = useRef(false);

  const [progressOverrides, setProgressOverrides] = useState<
    Record<string, number>
  >({});
  const saveTimersRef = useRef<Record<string, ReturnType<typeof setTimeout>>>(
    {}
  );
  const [citationsOpen, setCitationsOpen] = useState(false);

  // Project context for fetching action-items
  const projectId =
    typeof window !== "undefined"
      ? location.pathname.split("/projects/")[1]?.split("/")[0] || ""
      : "";
  const { data: projectActionItems } = useProjectActionItems(projectId, {
    type: "research",
    status: "completed",
  });
  const queryClient = useQueryClient();
  const invalidateSiift = useInvalidateSiift();

  // Always layer overrides onto parsed actions so UI shows latest local value until server echoes
  const parsedActions = parseActions(detail.actions || "");

  // Synthesize a stable research action from metadata, and merge without reordering user items
  const serverResearch = useMemo(() => {
    const list = Array.isArray(projectActionItems) ? projectActionItems : [];
    const candidates = list
      .map((it: any) => {
        let meta = it?.metadata || it?.meta;
        if (!meta && typeof it?.description === "string") {
          try {
            const parsed = JSON.parse(it.description);
            if (parsed && typeof parsed === "object") meta = parsed;
          } catch {}
        }
        const entryId = meta?.entryId ?? meta?.entry_id ?? meta?.entryID;
        const research = meta?.research || it?.research;
        const completedAt =
          it?.completedAt || it?.completed_at || it?.timestamp;
        if (String(entryId) !== String(detail.id)) return null;
        return { research, completedAt };
      })
      .filter(Boolean) as Array<{ research: any; completedAt?: string }>;

    if (candidates.length === 0) return null as any;
    // Pick latest by completedAt; fallback to longer facts
    candidates.sort((a, b) => {
      const ta = a.completedAt ? Date.parse(a.completedAt) : 0;
      const tb = b.completedAt ? Date.parse(b.completedAt) : 0;
      if (tb !== ta) return tb - ta;
      const fa = Array.isArray(a.research?.facts) ? a.research.facts.length : 0;
      const fb = Array.isArray(b.research?.facts) ? b.research.facts.length : 0;
      return fb - fa;
    });
    return candidates[0]?.research || null;
  }, [projectActionItems, detail.id]);

  const mergedActions = useMemo(() => {
    const base = [...parsedActions];

    // Build synthetic research action if research exists
    try {
      const raw = (detail as any)?.metadata;
      const meta =
        typeof raw === "string" && raw.trim().startsWith("{")
          ? JSON.parse(raw)
          : raw;
      const r = serverResearch || meta?.research;
      if (r) {
        const id = `research|${detail.id}`;
        const summary =
          typeof r.summary === "string" && r.summary.trim()
            ? r.summary.trim()
            : Array.isArray(r.facts) && r.facts[0]
            ? String(r.facts[0])
            : "Research";
        const conf =
          typeof r.confidence === "number"
            ? Math.max(0, Math.min(100, Math.round(r.confidence * 100)))
            : undefined;
        const derivedFromFacts = Array.isArray(r.facts)
          ? Math.min(100, r.facts.length * 20)
          : 0;
        const progress = Math.min(30, conf ?? derivedFromFacts);

        const researchText = (() => {
          const facts: string[] = Array.isArray(r.facts)
            ? r.facts.filter((f: any) => typeof f === "string" && f.trim())
            : [];
          const sources: string[] = Array.isArray(r.sources)
            ? r.sources.filter((s: any) => typeof s === "string" && s.trim())
            : [];
          const lines: string[] = [];
          facts.forEach((f, i) => {
            const idx = i + 1;
            const src = sources[i] || sources[0] || "";
            const citation = src ? ` [${idx}]` : "";
            const suffix = src ? ` (source: ${src})` : "";
            lines.push(`- ${f.trim()}${citation}${suffix}`);
          });
          if (sources.length && sources.length > facts.length) {
            lines.push("\nSources:");
            sources.forEach((s, i) => lines.push(`[${i + 1}] ${s}`));
          }
          return lines.join("\n");
        })();

        const synthetic: ActionItem = {
          id,
          type: "deep-research",
          content: summary,
          credit: 0,
          progress,
          status: progress === 100 ? "confirmed" : "result",
          results: [
            {
              id: `${id}|r0`,
              content: researchText || String((detail as any)?.result || ""),
              timestamp: new Date().toISOString(),
              type: "text",
            },
          ],
        } as ActionItem;

        const idx = base.findIndex((a) => a.id === id);
        if (idx >= 0) base[idx] = { ...base[idx], ...synthetic };
        else base.push(synthetic);
      }
    } catch {}

    return base;
  }, [
    parsedActions,
    serverResearch,
    (detail as any)?.metadata,
    (detail as any)?.result,
    detail?.id,
  ]);

  const actionItems = mergedActions.map((a) => ({
    ...a,
    progress: progressOverrides[a.id] ?? a.progress,
  }));

  // Persist merged synthetic research action once to the entry actions list (safe, idempotent)
  const mergedPersistedRef = useRef(false);
  useEffect(() => {
    if (mergedPersistedRef.current) return;
    if (mergedActions.length > parsedActions.length) {
      try {
        onSave(detail.id, "actions", actionsToJson(mergedActions));
      } catch {}
      mergedPersistedRef.current = true;
    }
  }, [mergedActions, parsedActions, detail.id, onSave]);

  // Build a research-derived result string with inline numeric citations
  const researchResultText = useMemo(() => {
    try {
      const raw = (detail as any)?.metadata;
      const parsed =
        typeof raw === "string" && raw.trim().startsWith("{")
          ? JSON.parse(raw)
          : raw;
      const research = parsed?.research;
      if (!research) return "";

      const facts: string[] = Array.isArray(research.facts)
        ? research.facts.filter((f: any) => typeof f === "string" && f.trim())
        : [];
      const sources: string[] = Array.isArray(research.sources)
        ? research.sources.filter((s: any) => typeof s === "string" && s.trim())
        : [];
      if (!facts.length && !sources.length) return "";

      const lines: string[] = [];
      facts.forEach((f, i) => {
        const idx = i + 1;
        const src = sources[i] || sources[0] || "";
        const citation = src ? ` [${idx}]` : "";
        const suffix = src ? ` (source: ${src})` : "";
        lines.push(`- ${f.trim()}${citation}${suffix}`);
      });

      if (sources.length && sources.length > facts.length) {
        lines.push("\nSources:");
        sources.forEach((s, i) => lines.push(`[${i + 1}] ${s}`));
      }

      return lines.join("\n");
    } catch {
      return "";
    }
  }, [detail?.id, (detail as any)?.metadata, detail?.result]);

  // Auto-apply research result into the entry's result field if empty (once per research content)
  const appliedResearchKeyRef = useRef<string | null>(null);
  useEffect(() => {
    const key = researchResultText || "";
    const currentResult = String((detail as any)?.result || "").trim();
    if (key && appliedResearchKeyRef.current !== key) {
      if (!currentResult) {
        try {
          onSave(detail.id, "result", key);
        } catch {}
      }
      appliedResearchKeyRef.current = key;
    }
  }, [detail?.id, detail?.result, researchResultText, onSave]);

  // Infer validation progress from research facts and update the first action
  useEffect(() => {
    try {
      const raw = (detail as any)?.metadata;
      const meta = typeof raw === "string" ? JSON.parse(raw) : raw;
      const research = meta?.research;
      const facts: string[] = Array.isArray(research?.facts)
        ? research.facts.filter((f: any) => typeof f === "string" && f.trim())
        : [];
      if (!facts.length || actionItems.length === 0) return;

      const target = actionItems[0];

      const derived = Math.min(
        95,
        Math.max(20, Math.round((facts.length / 5) * 100))
      );

      if (Number(target.progress) >= derived) return;

      const updated = actionItems.map((a) =>
        a.id === target.id ? ({ ...a, progress: derived } as ActionItem) : a
      );
      onSave(detail.id, "actions", actionsToJson(updated));
    } catch {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [researchResultText]);
  // Listen for research progress/completed events to reflect progress on first action
  const { setProgress, setCompleted } = useActionsStore();
  useEffect(() => {
    const targetKey = `entry:${detail.id}`;
    const onProgress = (e: Event) => {
      const ev = e as CustomEvent;
      const { entry_id, progress } = ev.detail || {};
      if (String(entry_id) !== String(detail.id)) return;
      const pct = Number(progress);
      if (Number.isFinite(pct))
        setProgress(targetKey, Math.max(0, Math.min(100, pct)));
    };
    const onCompleted = (e: Event) => {
      const ev = e as CustomEvent;
      const { entry_id } = ev.detail || {};
      if (String(entry_id) !== String(detail.id)) return;
      setCompleted(targetKey);

      // Invalidate related queries to fetch latest server state
      try {
        if (projectId) {
          queryClient.invalidateQueries({
            queryKey: ["project-action-items", projectId],
          });
          invalidateSiift(projectId);
        }
      } catch {}
    };
    if (typeof window !== "undefined") {
      window.addEventListener("siift:research-progress", onProgress);
      window.addEventListener("siift:research-completed", onCompleted);
    }
    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("siift:research-progress", onProgress);
        window.removeEventListener("siift:research-completed", onCompleted);
      }
    };
  }, [detail.id, setProgress, setCompleted]);
  // Reconcile overrides after props update: clear override when server/store reflects it
  useEffect(() => {
    const parsed = parseActions(detail.actions || "");
    setProgressOverrides((prev) => {
      let changed = false;
      const next = { ...prev };
      parsed.forEach((a) => {
        const ov = next[a.id];
        if (ov !== undefined && Number(a.progress) === Number(ov)) {
          delete next[a.id];
          changed = true;
        }
      });
      return changed ? next : prev;
    });
  }, [detail.actions]);

  const getActionProgress = (action: ActionItem) =>
    progressOverrides[action.id] ?? action.progress;

  const computeEntryStatusFromActions = (
    actions: ActionItem[]
  ): ProgressStatus => {
    const hasAnyAction = actions.length > 0;
    const anyHasResult = actions.some((a) => (a.results?.length || 0) > 0);
    const anyProgress100 = actions.some((a) => Number(a.progress) === 100);
    if (anyHasResult) {
      return anyProgress100 ? "confirmed" : "result";
    }
    return hasAnyAction ? "action" : "idea";
  };

  const handleProgressSave = () => {
    if (selectedAction) {
      const updatedActions = actionItems.map((item) =>
        item.id === selectedAction.id ? selectedAction : item
      );
      onSave(detail.id, "actions", actionsToJson(updatedActions));
    }
    setProgressOpen(false);
  };

  // Debounced result editing
  const [resultBuffers, setResultBuffers] = useState<Record<string, string>>(
    {}
  );
  const resultTimersRef = useRef<Record<string, ReturnType<typeof setTimeout>>>(
    {}
  );

  const scheduleResultSave = (
    actionId: string,
    resultId: string,
    content: string
  ) => {
    setResultBuffers((prev) => ({ ...prev, [actionId]: content }));
    const existing = resultTimersRef.current[actionId];
    if (existing) clearTimeout(existing);
    resultTimersRef.current[actionId] = setTimeout(() => {
      const latestBuffer = resultBuffers[actionId];
      const latest = (
        latestBuffer !== undefined ? latestBuffer : content
      ).trim();
      handleUpdateResult(actionId, resultId, latest);
      // don't clear resultBuffers here; wait for server echo and effect to reconcile
      delete resultTimersRef.current[actionId];
    }, 400);
  };

  // Flush buffered result text after a result object gets created (first keystroke path)
  useEffect(() => {
    const actions = actionItems;
    actions.forEach((a) => {
      const buffered = resultBuffers[a.id];
      if (buffered !== undefined && a.results && a.results[0]) {
        const r = a.results[0];
        const bufferedTrim = (buffered || "").trim();
        const serverTrim = (r.content || "").trim();
        if (serverTrim === bufferedTrim) {
          // server caught up → clear buffer for this action
          setResultBuffers((prev) => {
            const copy = { ...prev };
            delete copy[a.id];
            return copy;
          });
        } else if (serverTrim === "" && bufferedTrim !== "") {
          // result object exists but empty → push buffered content
          scheduleResultSave(a.id, r.id, buffered);
        }
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [detail.actions]);

  const handleUpdateResult = (
    actionId: string,
    resultId: string,
    content: string
  ) => {
    const action = actionItems.find((item) => item.id === actionId);
    if (action) {
      const updatedResults = action.results.map((result) =>
        result.id === resultId ? { ...result, content } : result
      );

      // Update status based on content and progress
      let newStatus: ProgressStatus;
      if (content.trim()) {
        newStatus = action.progress === 100 ? "confirmed" : "result";
      } else {
        newStatus = "action";
      }

      const updatedAction: ActionItem = {
        ...action,
        results: updatedResults,
        status: newStatus,
        progress: content.trim() ? action.progress : 0,
      };
      console.log("Updating result, new status:", newStatus);
      const updatedActions: ActionItem[] = actionItems.map((item) =>
        item.id === actionId ? updatedAction : item
      );
      console.log("Saving updated actions to backend:", updatedActions);
      console.log("JSON payload being sent:", actionsToJson(updatedActions));

      // Save both actions and update the business item status
      onSave(detail.id, "actions", actionsToJson(updatedActions));
      onSave(
        detail.id,
        "status",
        computeEntryStatusFromActions(updatedActions)
      );
    }
  };

  // Render helper to convert inline citation markers like [1] into clickable elements
  const renderTextWithCitations = (
    text: string,
    sources: string[]
  ): ReactElement => {
    const lines = (text || "")
      .split("\n")
      .filter((ln) => ln.trim() && ln.trim() !== "Sources:")
      .filter((ln) => !/^\s*\[\d+\]\s+https?:\/\//i.test(ln));

    const renderLine = (ln: string, lineIdx: number) => {
      const content = ln.replace(/^\s*-\s*/, "");
      const sanitized = content.replace(
        /\s*\(source:\s*https?:\/\/[^\s)]+\)/gi,
        ""
      );
      const parts: Array<string | ReactElement> = [];
      let lastIndex = 0;
      const regex = /\[(\d+)\]/g;
      let match: RegExpExecArray | null;
      while ((match = regex.exec(sanitized)) !== null) {
        const [token, numStr] = match;
        const idx = parseInt(numStr, 10) - 1;
        const url = sources[idx];
        const start = match.index;
        if (start > lastIndex) parts.push(sanitized.slice(lastIndex, start));
        parts.push(
          <button
            key={`cit-${lineIdx}-${start}`}
            onClick={(e) => {
              e.stopPropagation();
              if (url) window.open(url, "_blank", "noopener,noreferrer");
            }}
            className="underline text-[var(--primary)] hover:text-[var(--primary-dark)] hover:opacity-90 cursor-pointer focus:outline-none"
          >
            [{idx + 1}]
          </button>
        );
        lastIndex = start + token.length;
      }
      if (lastIndex < sanitized.length) parts.push(sanitized.slice(lastIndex));
      return (
        <li
          key={`ln-${lineIdx}`}
          className="text-sm leading-snug text-[var(--foreground)]"
        >
          {parts}
        </li>
      );
    };

    const items = lines.map((ln, i) => renderLine(ln, i));
    return <ul className="space-y-2 list-disc pl-5">{items}</ul>;
  };

  return (
    <div className={disabled ? "opacity-50 pointer-events-none" : ""}>
      {/* Action Items */}
      {actionItems.length > 0 && (
        <div className="space-y-2">
          {actionItems.map((action) => {
            const hasResult = action.results.length > 0;
            const result = hasResult ? action.results[0] : null;
            const hasNonEmptyResult = Boolean(result?.content?.trim());
            const displayProgress = hasNonEmptyResult
              ? progressOverrides[action.id] ?? getActionProgress(action)
              : 0;

            return (
              <div
                key={action.id}
                role="button"
                onClick={() => {
                  setSelectedAction(action);
                  setSidebarActionDraft(action.content || "");
                  const r0 = (action.results && action.results[0]) || null;
                  let initialResult = r0?.content || "";
                  if (!initialResult) {
                    try {
                      const raw = (detail as any)?.metadata;
                      const meta =
                        typeof raw === "string" && raw.trim().startsWith("{")
                          ? JSON.parse(raw)
                          : raw;
                      const s = meta?.research?.summary;
                      if (typeof s === "string" && s.trim())
                        initialResult = s.trim();
                    } catch {}
                  }
                  setSidebarResultDraft(initialResult);
                  sidebarSavedRef.current = false;
                  setActionSheetOpen(true);
                }}
                className="rounded border border-[var(--siift-light-mid)]/50  bg-[var(--background)] px-2 py-2 hover:bg-[var(--accent)]/10  hover:border-[var(--accent)] cursor-pointer transition-colors"
              >
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-[var(--muted-foreground)]">
                      {action.type.toUpperCase()}
                    </span>
                    <span
                      className={`text-xs ${
                        displayProgress > 90
                          ? " text-[var(--primary)]"
                          : displayProgress > 50
                          ? " text-[var(--siift-darkest)]"
                          : "text-[var(--destructive)]"
                      }`}
                    >
                      {displayProgress}%
                    </span>
                  </div>
                  <p className=" text-sm leading-tight text-[var(--foreground)] break-words">
                    {action.content}
                  </p>
                  {/* Research result card (compact) */}
                  {(() => {
                    try {
                      const raw = (detail as any)?.metadata;
                      const meta =
                        typeof raw === "string" && raw.trim().startsWith("{")
                          ? JSON.parse(raw)
                          : raw;
                      const r = meta?.research;
                      if (!r) return null;
                      const summary =
                        typeof r.summary === "string" && r.summary.trim()
                          ? r.summary.trim()
                          : undefined;
                      const facts = Array.isArray(r.facts)
                        ? r.facts
                            .filter(
                              (f: any) => typeof f === "string" && f.trim()
                            )
                            .slice(0, 3)
                        : [];
                      const sources = Array.isArray(r.sources)
                        ? r.sources.filter(
                            (s: any) => typeof s === "string" && s.trim()
                          )
                        : [];
                      const conf =
                        typeof r.confidence === "number"
                          ? Math.max(
                              0,
                              Math.min(100, Math.round(r.confidence * 100))
                            )
                          : undefined;
                      if (!summary && facts.length === 0) return null;
                      return (
                        <div className="mt-2 rounded border border-[var(--siift-light-mid)]/40 bg-[var(--background)]/60 p-2">
                          {summary && (
                            <div className="text-xs font-medium text-[var(--foreground)] line-clamp-2">
                              {summary}
                            </div>
                          )}
                          {facts.length > 0 && (
                            <ul className="mt-1 space-y-1">
                              {facts.map((f: string, i: number) => (
                                <li
                                  key={i}
                                  className="text-[11px] leading-snug text-[var(--muted-foreground)]"
                                >
                                  • {f}
                                  {sources[i] && (
                                    <a
                                      href={sources[i]}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="ml-1 underline text-[var(--primary)]"
                                    >
                                      [{i + 1}]
                                    </a>
                                  )}
                                </li>
                              ))}
                            </ul>
                          )}
                          {conf !== undefined && (
                            <div className="mt-1 text-[10px] text-[var(--muted-foreground)]">
                              Confidence: {conf}%
                            </div>
                          )}
                        </div>
                      );
                    } catch {
                      return null;
                    }
                  })()}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Manual Action creation moved to Suggestions column */}

      {/* Progress Management Sheet */}
      <Sheet open={progressOpen} onOpenChange={setProgressOpen}>
        <SheetContent
          side="bottom"
          className="max-w-[50vw] mx-auto rounded-t-xl p-6"
        >
          <SheetHeader className="text-center">
            <SheetTitle>Action Status</SheetTitle>
          </SheetHeader>

          <div className="py-4 space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select
                value={selectedAction?.status || "idea"}
                onValueChange={(value: ProgressStatus) =>
                  setSelectedAction((prev) =>
                    prev ? { ...prev, status: value } : null
                  )
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="idea" className="flex items-center gap-2">
                    <Circle className="h-4 w-4" />
                    Idea
                  </SelectItem>
                  <SelectItem
                    value="action"
                    className="flex items-center gap-2"
                  >
                    <Clock className="h-4 w-4" />
                    Action
                  </SelectItem>
                  <SelectItem
                    value="result"
                    className="flex items-center gap-2"
                  >
                    <CheckCircle className="h-4 w-4" />
                    Result
                  </SelectItem>
                  <SelectItem
                    value="confirmed"
                    className="flex items-center gap-2"
                  >
                    <AlertCircle className="h-4 w-4" />
                    Confirmed
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <SheetFooter>
            <div className="flex gap-3 w-full">
              <Button
                variant="outline"
                onClick={() => setProgressOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button onClick={handleProgressSave} className="flex-1">
                Save Progress
              </Button>
            </div>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* New Action creation moved to Suggestions column */}

      {/* Action Details Sheet */}
      <Sheet
        open={actionSheetOpen}
        onOpenChange={(open) => {
          if (!open && selectedAction) {
            // Commit on close if there are changes and not already saved
            try {
              const current =
                actionItems.find((a) => a.id === selectedAction.id) ||
                selectedAction;
              const currentResult =
                (current.results && current.results[0]) || null;
              const hasActionChanged =
                (sidebarActionDraft ?? "") !== (current.content ?? "");
              const hasResultChanged =
                (sidebarResultDraft ?? "") !== (currentResult?.content ?? "");
              if (
                !sidebarSavedRef.current &&
                (hasActionChanged || hasResultChanged)
              ) {
                const updatedAction: ActionItem = {
                  ...current,
                  content: sidebarActionDraft ?? "",
                  results: currentResult
                    ? [{ ...currentResult, content: sidebarResultDraft ?? "" }]
                    : ([
                        {
                          id: crypto.randomUUID(),
                          content: sidebarResultDraft ?? "",
                          timestamp: new Date().toISOString(),
                          type: "text",
                        },
                      ] as any),
                } as ActionItem;
                // Update status/progress based on result content
                const hasNonEmpty = Boolean((sidebarResultDraft ?? "").trim());
                const nextProgress = hasNonEmpty ? updatedAction.progress : 0;
                const nextStatus = hasNonEmpty
                  ? nextProgress === 100
                    ? "confirmed"
                    : "result"
                  : "action";
                const finalAction: ActionItem = {
                  ...updatedAction,
                  progress: nextProgress,
                  status: nextStatus,
                };
                const updatedActions = actionItems.map((it) =>
                  it.id === finalAction.id ? finalAction : it
                );
                onSave(detail.id, "actions", actionsToJson(updatedActions));
                try {
                  const applied = finalAction.results?.[0]?.content || "";
                  if (applied.trim()) onSave(detail.id, "result", applied);
                } catch {}
              }
            } finally {
              setActionSheetOpen(false);
              setSelectedAction(null);
            }
          } else {
            setActionSheetOpen(open);
          }
        }}
      >
        <SheetContent side="right" className="w-[520px] max-w-[90vw] p-0">
          <SheetHeader>
            <SheetTitle className="text-md">Action Details</SheetTitle>
          </SheetHeader>

          {selectedAction &&
            (() => {
              const current =
                actionItems.find((a) => a.id === selectedAction.id) ||
                selectedAction;
              const currentResult =
                (current.results && current.results[0]) || null;
              const hasNonEmptyResult = Boolean(currentResult?.content?.trim());
              const hasResultOrDraft =
                hasNonEmptyResult || Boolean((sidebarResultDraft ?? "").trim());
              const displayProgress = hasResultOrDraft
                ? progressOverrides[current.id] ?? getActionProgress(current)
                : 0;
              const lastUpdatedIso =
                (currentResult && currentResult.timestamp) || detail.updatedAt;
              const lastUpdated = lastUpdatedIso
                ? new Date(lastUpdatedIso).toLocaleString()
                : null;

              // Detect research/citations presence
              let sourcesFromMeta: string[] = [];
              let summaryFromMeta: string | undefined;
              try {
                const raw = (detail as any)?.metadata;
                const meta =
                  typeof raw === "string" && raw.trim().startsWith("{")
                    ? JSON.parse(raw)
                    : raw;
                const r = meta?.research;
                summaryFromMeta =
                  typeof r?.summary === "string" && r.summary.trim()
                    ? r.summary.trim()
                    : undefined;
                sourcesFromMeta = Array.isArray(r?.sources)
                  ? r.sources.filter(
                      (s: any) => typeof s === "string" && s.trim()
                    )
                  : [];
              } catch {}

              const resultTextCombined = (
                (sidebarResultDraft ?? "") ||
                (currentResult?.content ?? "")
              ).toString();
              const hasInlineSource = /\(source:\s*https?:\/\//i.test(
                resultTextCombined
              );
              const hasBracketCitations = /\[[0-9]+\]/.test(resultTextCombined);
              const sourcesDetectedFromText: string[] = (() => {
                const found: string[] = [];
                const regex = /\(source:\s*(https?:\/\/[^\s)]+)\)/gi;
                let m;
                while ((m = regex.exec(resultTextCombined))) {
                  const url = (m[1] || "").trim();
                  if (url && !found.includes(url)) found.push(url);
                }
                return found;
              })();
              const sources = (
                sourcesFromMeta.length
                  ? sourcesFromMeta
                  : sourcesDetectedFromText
              ).slice(0, 50);
              const hasCitations =
                sources.length > 0 || hasInlineSource || hasBracketCitations;

              return (
                <div className="space-y-6 px-4 pb-4">
                  {/* Summary (read-only) when citations exist; otherwise Action/Result editors */}
                  {hasCitations ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <label className="text-sm font-medium text-[var(--muted-foreground)] my-3 block">
                          Summary
                        </label>
                      </div>
                      {summaryFromMeta ? (
                        <div className="text-sm leading-snug text-[var(--foreground)]">
                          {/* Render inline clickable [n] markers inside summary */}
                          {(() => {
                            const content = summaryFromMeta || "";
                            const parts: Array<string | ReactElement> = [];
                            let lastIndex = 0;
                            const regex = /\[(\d+)\]/g;
                            let match: RegExpExecArray | null;
                            while ((match = regex.exec(content)) !== null) {
                              const [token, numStr] = match;
                              const idx = parseInt(numStr, 10) - 1;
                              const url = sources[idx];
                              const start = match.index;
                              if (start > lastIndex)
                                parts.push(content.slice(lastIndex, start));
                              parts.push(
                                <button
                                  key={`sum-cit-${start}`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (url)
                                      window.open(
                                        url,
                                        "_blank",
                                        "noopener,noreferrer"
                                      );
                                  }}
                                  className="underline text-[var(--primary)] hover:text-[var(--primary-dark)] hover:opacity-90 cursor-pointer focus:outline-none"
                                >
                                  [{idx + 1}]
                                </button>
                              );
                              lastIndex = start + token.length;
                            }
                            if (lastIndex < content.length)
                              parts.push(content.slice(lastIndex));
                            return <span>{parts}</span>;
                          })()}
                        </div>
                      ) : (
                        <div className="text-sm leading-snug">
                          {renderTextWithCitations(resultTextCombined, sources)}
                        </div>
                      )}
                    </div>
                  ) : (
                    <>
                      {/* Action text */}
                      <div>
                        <label className="text-sm font-medium text-[var(--muted-foreground)] my-3 block">
                          Action
                        </label>
                        <Textarea
                          value={sidebarActionDraft}
                          onChange={(e) =>
                            setSidebarActionDraft(e.target.value)
                          }
                          className="min-h-[100px] border border-[var(--siift-light-mid)]/50 rounded-md p-2"
                        />
                      </div>

                      {/* Result */}
                      <div>
                        <label className="text-sm font-medium text-[var(--muted-foreground)] my-3 block">
                          Result
                        </label>
                        <Textarea
                          value={sidebarResultDraft}
                          placeholder="Enter result..."
                          className="min-h-[100px] text-sm border border-[var(--siift-light-mid)]/50 rounded-md p-2"
                          onChange={(e) => {
                            const content = e.target.value;
                            setSidebarResultDraft(content);

                            // Debounced auto-save for result text
                            if (selectedAction) {
                              const currentResult =
                                (selectedAction.results &&
                                  selectedAction.results[0]) ||
                                null;
                              if (currentResult) {
                                // Update existing result
                                scheduleResultSave(
                                  selectedAction.id,
                                  currentResult.id,
                                  content
                                );
                              } else if (content.trim()) {
                                // Create new result if content is not empty
                                const newResultId = crypto.randomUUID();
                                const updatedAction: ActionItem = {
                                  ...selectedAction,
                                  results: [
                                    {
                                      id: newResultId,
                                      content: content,
                                      timestamp: new Date().toISOString(),
                                      type: "text",
                                    },
                                  ],
                                  status: "result",
                                };
                                const updatedActions = actionItems.map((item) =>
                                  item.id === selectedAction.id
                                    ? updatedAction
                                    : item
                                );
                                onSave(
                                  detail.id,
                                  "actions",
                                  actionsToJson(updatedActions)
                                );
                              }
                            }
                          }}
                        />
                      </div>
                    </>
                  )}

                  {/* Citations (collapsible) */}
                  {hasCitations && (
                    <Collapsible
                      open={citationsOpen}
                      onOpenChange={setCitationsOpen}
                    >
                      <div
                        className="rounded-md border border-[var(--siift-light-mid)]/50 bg-[var(--background)]/60 p-3"
                        onMouseEnter={() => setCitationsOpen(true)}
                        onMouseLeave={() => setCitationsOpen(false)}
                      >
                        <div className="flex items-center justify-between">
                          <label className="text-sm font-medium text-[var(--muted-foreground)] mb-0 flex items-center">
                            Citations
                          </label>
                          <CollapsibleTrigger className="text-base leading-none text-[var(--primary)] flex items-center md:hidden">
                            {citationsOpen ? "−" : "+"}
                          </CollapsibleTrigger>
                        </div>
                        <CollapsibleContent>
                          <ul className="space-y-3 list-decimal list-inside mt-3">
                            {sources.map((s: string, i: number) => (
                              <div
                                key={i}
                                className="justify-between flex items-center bg-[var(--siift-light-accent)]/10 border border-[var(--siift-light-mid)]/50 rounded-md p-1 hover:bg-[var(--siift-light-accent)]/20 transition-colors"
                              >
                                <li className="text-sm text-[var(--muted-foreground)]">
                                  <a
                                    href={s}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="underline text-[var(--primary)] break-all"
                                  >
                                    {s.length > 50 ? s.slice(0, 50) + "..." : s}
                                  </a>
                                </li>
                                <ArrowUpRight className="w-3 h-3" />
                              </div>
                            ))}
                          </ul>
                        </CollapsibleContent>
                      </div>
                    </Collapsible>
                  )}

                  {/* Progress */}

                  {hasNonEmptyResult &&
                    current.type !== "web-search" &&
                    current.type !== "deep-research" && (
                      <div>
                        <div className="flex items-center justify-between text-sm mb-1 text-[var(--text-muted)] ">
                          <span>Validation</span>
                          <span>{displayProgress}%</span>
                        </div>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={
                            progressOverrides[current.id] ?? displayProgress
                          }
                          onChange={(e) => {
                            const newProgress = parseInt(e.target.value) || 0;
                            // Optimistically update the selectedAction so the UI reflects immediately
                            setSelectedAction((prev) =>
                              prev
                                ? {
                                    ...prev,
                                    progress: newProgress,
                                    status:
                                      (prev.results?.length || 0) > 0
                                        ? newProgress === 100
                                          ? "confirmed"
                                          : "result"
                                        : "action",
                                  }
                                : prev
                            );
                            setProgressOverrides((prev) => ({
                              ...prev,
                              [current.id]: newProgress,
                            }));
                            const existingTimer =
                              saveTimersRef.current[current.id];
                            if (existingTimer) clearTimeout(existingTimer);
                            saveTimersRef.current[current.id] = setTimeout(
                              () => {
                                const shouldCreateResult =
                                  (current.results?.length || 0) === 0 &&
                                  Boolean((sidebarResultDraft ?? "").trim());
                                const updatedAction = {
                                  ...current,
                                  results: shouldCreateResult
                                    ? [
                                        {
                                          id: crypto.randomUUID(),
                                          content: (
                                            sidebarResultDraft ?? ""
                                          ).trim(),
                                          timestamp: new Date().toISOString(),
                                          type: "text",
                                        },
                                      ]
                                    : current.results,
                                  progress: newProgress,
                                  status:
                                    shouldCreateResult ||
                                    (current.results?.length || 0) > 0
                                      ? newProgress === 100
                                        ? "confirmed"
                                        : "result"
                                      : "action",
                                } as ActionItem;
                                const updatedActions = actionItems.map((item) =>
                                  item.id === current.id ? updatedAction : item
                                );
                                onSave(
                                  detail.id,
                                  "actions",
                                  actionsToJson(updatedActions)
                                );
                              },
                              400
                            );
                          }}
                          className="w-full h-2 bg-[var(--siift-light-mid)] rounded-full appearance-none cursor-pointer slider"
                          style={{
                            background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${displayProgress}%, var(--siift-light-mid) ${displayProgress}%, var(--siift-light-mid) 100%)`,
                          }}
                        />
                        <style jsx>{`
                          .slider::-webkit-slider-thumb {
                            appearance: none;
                            height: 16px;
                            width: 16px;
                            border-radius: 50%;
                            background: var(--primary);
                            cursor: pointer;
                            border: 2px solid var(--background);
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                          }
                          .slider::-moz-range-thumb {
                            height: 16px;
                            width: 16px;
                            border-radius: 50%;
                            background: var(--primary);
                            cursor: pointer;
                            border: 2px solid var(--background);
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                          }
                        `}</style>
                      </div>
                    )}

                  <div className="flex items-center justify-between gap-2 pt-2">
                    {lastUpdated && (
                      <div className="text-xs text-[var(--muted-foreground)]">
                        Last updated {lastUpdated}
                      </div>
                    )}
                    <div className="text-xs text-[var(--muted-foreground)]"></div>
                    <div className="flex gap-2">
                      <IButton
                        variant="outline"
                        onClick={() => {
                          sidebarSavedRef.current = true;
                          setActionSheetOpen(false);
                          setSelectedAction(null);
                        }}
                      >
                        Cancel
                      </IButton>
                      <IButton
                        variant="outline"
                        onClick={() => {
                          if (!selectedAction) return;
                          const current =
                            actionItems.find(
                              (a) => a.id === selectedAction.id
                            ) || selectedAction;
                          const currentResult =
                            (current.results && current.results[0]) || null;
                          const updatedAction: ActionItem = {
                            ...current,
                            content: sidebarActionDraft ?? "",
                            results: currentResult
                              ? [
                                  {
                                    ...currentResult,
                                    content: sidebarResultDraft ?? "",
                                  },
                                ]
                              : ([
                                  {
                                    id: crypto.randomUUID(),
                                    content: sidebarResultDraft ?? "",
                                    timestamp: new Date().toISOString(),
                                    type: "text",
                                  },
                                ] as any),
                          } as ActionItem;
                          const hasNonEmpty = Boolean(
                            (sidebarResultDraft ?? "").trim()
                          );
                          const nextProgress = hasNonEmpty
                            ? updatedAction.progress
                            : 0;
                          const nextStatus = hasNonEmpty
                            ? nextProgress === 100
                              ? "confirmed"
                              : "result"
                            : "action";
                          const finalAction: ActionItem = {
                            ...updatedAction,
                            progress: nextProgress,
                            status: nextStatus,
                          };
                          const updatedActions = actionItems.map((it) =>
                            it.id === finalAction.id ? finalAction : it
                          );
                          onSave(
                            detail.id,
                            "actions",
                            actionsToJson(updatedActions)
                          );
                          try {
                            const applied =
                              finalAction.results?.[0]?.content || "";
                            if (applied.trim())
                              onSave(detail.id, "result", applied);
                          } catch {}
                          sidebarSavedRef.current = true;
                          setActionSheetOpen(false);
                          setSelectedAction(null);
                        }}
                      >
                        Save
                      </IButton>
                    </div>
                  </div>
                </div>
              );
            })()}
        </SheetContent>
      </Sheet>
    </div>
  );
}
