"use client";

import { create } from "zustand";

export type AiDebugEvent = {
  id: string;
  projectId: string;
  sessionId?: string | null;
  name: string; // SSE event name
  timestamp: string; // ISO string
  // Keep payload lightweight to avoid heavy renders
  payload?: Record<string, unknown> | unknown;
};

type AiDebugState = {
  eventsByProject: Record<string, AiDebugEvent[]>;
  addEvent: (projectId: string, event: Omit<AiDebugEvent, "projectId">) => void;
  clearEvents: (projectId: string) => void;
  getEvents: (projectId: string) => AiDebugEvent[];
};

const MAX_EVENTS_PER_PROJECT = 200;

export const useAiDebugStore = create<AiDebugState>((set, get) => ({
  eventsByProject: {},

  addEvent: (projectId, event) => {
    set((state) => {
      const list = state.eventsByProject[projectId] || [];
      const next = [...list, { ...event, projectId }];
      // Trim to max size (keep most recent)
      const trimmed =
        next.length > MAX_EVENTS_PER_PROJECT
          ? next.slice(next.length - MAX_EVENTS_PER_PROJECT)
          : next;
      return {
        eventsByProject: { ...state.eventsByProject, [projectId]: trimmed },
      };
    });
  },

  clearEvents: (projectId) => {
    set((state) => {
      if (!state.eventsByProject[projectId]) return state;
      const clone = { ...state.eventsByProject };
      clone[projectId] = [];
      return { eventsByProject: clone } as AiDebugState;
    });
  },

  getEvents: (projectId) => {
    // Return a stable empty array reference to avoid infinite re-render loops
    // when used inside a selector. Creating a new [] each render would cause
    // useSyncExternalStore to think the snapshot changed.
    const EMPTY_EVENTS: AiDebugEvent[] =
      (useAiDebugStore as any).__EMPTY__ ||
      ((useAiDebugStore as any).__EMPTY__ = []);
    return get().eventsByProject[projectId] || EMPTY_EVENTS;
  },
}));
