"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import { useChatStore } from "@/stores/chatStore";
import type { ChatMessage } from "@/types/Chat.types";
import {
  addNewEntriesForTopic,
  addNewEntriesForTopicViaLookup,
  markTopicHasNew,
} from "@/lib/topicNew";
import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { useAiDebugStore } from "@/stores/aiDebugStore";

export function useAiChat(
  projectId: string,
  options?: { topicName?: string | null }
) {
  const PLACEHOLDER_GENERATING = "Sifting...";
  const {
    addMessage,
    updateLastMessage,
    appendToLastMessage,
    setProjectLoading,
    setProjectStreaming,
    getProjectLoading,
    getProjectStreaming,
    chatSession,
    setChatSession,
    setScope,
  } = useChatStore();

  const [sessionId, setSessionId] = useState<string | null>(null);
  const esRef = useRef<EventSource | null>(null);
  const addDebugEvent = useAiDebugStore((s) => s.addEvent);

  // Initialize session ID from chat session if available
  useEffect(() => {
    if (chatSession?.id && !sessionId) {
      setSessionId(chatSession.id);
    }
  }, [chatSession?.id, sessionId]);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Close EventSource on hard refresh/navigation to avoid server-side orphan jobs
  useEffect(() => {
    const onBeforeUnload = () => {
      try {
        esRef.current?.close();
      } catch {}
      esRef.current = null;
    };
    if (typeof window !== "undefined") {
      window.addEventListener("beforeunload", onBeforeUnload);
    }
    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("beforeunload", onBeforeUnload);
      }
    };
  }, []);

  // Keep scope in sync with project/topic selection
  useEffect(() => {
    try {
      setScope?.(projectId, options?.topicName ?? null);
    } catch {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId, options?.topicName]);

  // Resume existing session on load/refresh without creating a new backend stream
  useEffect(() => {
    let cancelled = false;
    (async () => {
      if (!projectId) {
        console.log(
          `[useAiChat-${projectId}] No projectId, skipping session resume`
        );
        return;
      }

      // Validate projectId format (should be UUID)
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(projectId)) {
        console.warn(
          `[useAiChat-${projectId}] Invalid projectId format, skipping session resume`
        );
        return;
      }
      if (esRef.current) {
        console.log(
          `[useAiChat-${projectId}] Already connected to stream, skipping`
        );
        return; // already connected
      }

      console.log(`[useAiChat-${projectId}] Starting session resume process`);

      try {
        const cached =
          typeof window !== "undefined"
            ? window.localStorage.getItem(`chat_session_${projectId}`)
            : null;

        console.log(
          `[useAiChat-${projectId}] Cached session from localStorage:`,
          cached
        );
        console.log(
          `[useAiChat-${projectId}] Current sessionId state:`,
          sessionId
        );

        let sessId: string | null = cached || sessionId || null;

        if (!sessId) {
          console.log(
            `[useAiChat-${projectId}] No cached session, fetching from API...`
          );
          // Discover latest active session for this project (no stream creation)
          const resp = await fetch(
            `/api/ai-chat/projects/${projectId}/sessions`,
            { credentials: "include" }
          );
          console.log(
            `[useAiChat-${projectId}] API response status:`,
            resp.status,
            resp.statusText
          );

          if (resp.ok) {
            const root = await resp.json();
            console.log(`[useAiChat-${projectId}] Raw API response:`, root);

            const list: Array<any> = Array.isArray(root)
              ? root
              : Array.isArray(root?.sessions)
              ? root.sessions
              : Array.isArray(root?.data)
              ? root.data
              : [];
            console.log(`[useAiChat-${projectId}] Parsed sessions list:`, list);

            const actives = list.filter((s: any) => s?.status === "active");
            console.log(`[useAiChat-${projectId}] Active sessions:`, actives);

            // Prefer sessions with conversationStage === 'siift_generated' and has_siift_data true
            const siiftGenerated = actives.filter((s: any) => {
              const sessionData = s?.session_data;
              const conversationStage = sessionData?.conversationStage;
              const hasSiiftData = sessionData?.siiftTablesGenerated;
              return conversationStage === "siift_generated" && hasSiiftData;
            });
            console.log(
              `[useAiChat-${projectId}] siift-generated sessions:`,
              siiftGenerated
            );

            // Use siift-generated sessions if available, otherwise fall back to all actives
            const candidateSessions =
              siiftGenerated.length > 0 ? siiftGenerated : actives;
            console.log(
              `[useAiChat-${projectId}] Candidate sessions for selection:`,
              candidateSessions
            );

            const sorted = candidateSessions.sort((a: any, b: any) => {
              const at = new Date(
                a?.updatedAt || a?.updated_at || a?.createdAt || 0
              ).getTime();
              const bt = new Date(
                b?.updatedAt || b?.updated_at || b?.createdAt || 0
              ).getTime();
              return bt - at;
            });
            console.log(
              `[useAiChat-${projectId}] Sorted candidate sessions (newest first):`,
              sorted
            );

            const chosen = sorted[0] || list[0];
            console.log(`[useAiChat-${projectId}] Chosen session:`, chosen);

            if (chosen) {
              sessId =
                chosen.id || chosen.session_id || chosen.sessionId || null;
              console.log(
                `[useAiChat-${projectId}] Extracted session ID:`,
                sessId
              );
            } else {
              console.log(
                `[useAiChat-${projectId}] No sessions found to resume`
              );
            }
          } else {
            console.log(
              `[useAiChat-${projectId}] API request failed:`,
              resp.status,
              resp.statusText
            );
          }
        } else {
          console.log(
            `[useAiChat-${projectId}] Using existing session ID:`,
            sessId
          );
        }

        if (!cancelled && sessId) {
          console.log(
            `[useAiChat-${projectId}] Resuming with session ID:`,
            sessId
          );
          setSessionId(sessId);
          try {
            window.localStorage.setItem(`chat_session_${projectId}`, sessId);
            console.log(
              `[useAiChat-${projectId}] Saved session to localStorage`
            );
          } catch (e) {
            console.warn(
              `[useAiChat-${projectId}] Failed to save to localStorage:`,
              e
            );
          }
          // Only re-subscribe to SSE; DO NOT send a chat request here
          console.log(
            `[useAiChat-${projectId}] Opening SSE stream for session:`,
            sessId
          );
          openStream(sessId);
        } else {
          console.log(
            `[useAiChat-${projectId}] No session to resume or operation cancelled`
          );
        }
      } catch (error) {
        console.error(
          `[useAiChat-${projectId}] Error during session resume:`,
          error
        );
      }
    })();
    return () => {
      cancelled = true;
    };
    // we intentionally omit openStream from deps to avoid reconnect loops
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]);

  // Subscribe reactively to per-project loading/streaming states
  const isLoading = useChatStore(
    (s) => s.projectLoadingStates[projectId] || false
  );
  const isStreaming = useChatStore(
    (s) => s.projectStreamingStates[projectId] || false
  );
  // Dedupe guards for streaming
  const processedEventsRef = useRef<Set<string>>(new Set());
  const lastAppendedChunkRef = useRef<string | null>(null);
  const tokenStreamAccumulatedLengthRef = useRef<number>(0);

  const ensureAiMessage = useCallback(() => {
    try {
      const state = (useChatStore as any).getState?.();
      const msgs = state?.messages as ChatMessage[] | undefined;
      const last = msgs && msgs.length > 0 ? msgs[msgs.length - 1] : undefined;
      // Only create frame if none exists or last is user
      if (!last || last.isCurrentUser) {
        updateLastMessage(PLACEHOLDER_GENERATING);
      }
    } catch {
      updateLastMessage(PLACEHOLDER_GENERATING);
    }
  }, [updateLastMessage]);

  const openStream = useCallback(
    (sessId: string) => {
      if (!sessId) return;
      // If an EventSource is already open, replace it to avoid stale state
      if (esRef.current) {
        try {
          esRef.current.close();
        } catch (error) {
          console.error("Error closing EventSource:", error);
        }
        esRef.current = null;
      }

      // Open SSE to receive tokens before sending chat
      const es = new EventSource(`/api/ai-chat/sessions/${sessId}/events`, {
        withCredentials: true,
      });
      esRef.current = es;

      // Normalize text chunks for better Markdown rendering (lists/newlines)
      const normalizeForMarkdown = (text: string): string => {
        if (!text) return text;
        let out = String(text).replace(/\r\n?/g, "\n");
        // Convert leading bullet '•' to markdown '-'
        out = out
          .split("\n")
          .map((line) => line.replace(/^\s*•\s?/, "- "))
          .join("\n");
        return out;
      };

      es.addEventListener("token_stream", (ev) => {
        try {
          const data = JSON.parse((ev as MessageEvent).data as string);
          addDebugEvent(projectId, {
            id: `${Date.now()}-token_stream`,
            sessionId: sessId,
            name: "token_stream",
            timestamp: new Date().toISOString(),
            payload: data,
          });
          const agent = data.agent || data.agent_name;
          const agentName = String(agent || "").toLowerCase();
          // Ignore tool/suggestion/siift generator streams for chat UI streaming state
          if (
            agentName.includes("tools") ||
            agentName.includes("siift_generator") ||
            agentName.includes("suggest")
          )
            return;
          if (data.tokens && Array.isArray(data.tokens)) {
            const tokensArr = (data.tokens as unknown[]).filter(
              (t: unknown) =>
                typeof t === "string" && !String(t).startsWith("TOOL:")
            ) as string[];
            const normalizedTokens = tokensArr.map((t) =>
              normalizeForMarkdown(t)
            );
            const snapshot = normalizedTokens.join("");
            const formattedSnapshot = normalizedTokens.join("\n\n");
            if (!snapshot) return;

            const prevLen = tokenStreamAccumulatedLengthRef.current || 0;
            const nextLen = snapshot.length;
            if (nextLen <= prevLen) return; // nothing new

            const delta = snapshot.slice(prevLen);
            tokenStreamAccumulatedLengthRef.current = nextLen;

            console.log(`[useAiChat-${projectId}] Token stream received:`, {
              agent: agentName,
              deltaLength: delta.length,
              snapshotLength: snapshot.length,
              delta: delta.slice(0, 50) + (delta.length > 50 ? "..." : ""),
            });

            setProjectStreaming(projectId, true);
            ensureAiMessage();

            // If placeholder is visible, replace it with the first snapshot chunk (formatted)
            try {
              const { messages } = (useChatStore as any).getState?.() || {};
              const last =
                Array.isArray(messages) && messages.length > 0
                  ? messages[messages.length - 1]
                  : null;
              if (
                last &&
                last.isCurrentUser === false &&
                (String(last.message) === PLACEHOLDER_GENERATING ||
                  String(last.message) === "Sifting...")
              ) {
                // Replace placeholder content with current formatted snapshot (not just delta)
                updateLastMessage(formattedSnapshot);
                return;
              }
            } catch {}
            // Keep updating the AI message with the full formatted snapshot so each token appears on new lines
            updateLastMessage(formattedSnapshot);
          }
        } catch {}
      });

      es.addEventListener("token", (ev) => {
        try {
          const data = JSON.parse((ev as MessageEvent).data as string);
          addDebugEvent(projectId, {
            id: `${Date.now()}-token`,
            sessionId: sessId,
            name: "token",
            timestamp: new Date().toISOString(),
            payload: data,
          });
          const chunk = String(data.content || data.token || "")
            .replace(/\r\n?/g, "\n")
            .split("\n")
            .map((line) => line.replace(/^\s*•\s?/, "- "))
            .join("\n");
          const agentName = String(
            data.agent || data.agent_name || ""
          ).toLowerCase();
          const eventId = `${data.agent}-${data.timestamp}-${chunk.slice(
            0,
            20
          )}`;
          if (processedEventsRef.current.has(eventId)) return;
          processedEventsRef.current.add(eventId);

          if (chunk.startsWith("TOOL:")) return;
          // Ignore tool/suggestion/siift generator tokens for chat UI streaming state
          if (
            agentName.includes("tools") ||
            agentName.includes("siift_generator") ||
            agentName.includes("suggest")
          )
            return;
          if (lastAppendedChunkRef.current === chunk) return;
          lastAppendedChunkRef.current = chunk;

          console.log(`[useAiChat-${projectId}] Token received:`, {
            agent: agentName,
            chunkLength: chunk.length,
            chunk: chunk.slice(0, 50) + (chunk.length > 50 ? "..." : ""),
          });

          setProjectStreaming(projectId, true);
          ensureAiMessage();

          // If placeholder is visible, replace it with this chunk instead of appending
          try {
            const { messages } = (useChatStore as any).getState?.() || {};
            const last =
              Array.isArray(messages) && messages.length > 0
                ? messages[messages.length - 1]
                : null;
            if (
              last &&
              last.isCurrentUser === false &&
              (String(last.message) === PLACEHOLDER_GENERATING ||
                String(last.message) === "Sifting...")
            ) {
              updateLastMessage(chunk);
              return;
            }
          } catch {}

          // Append with a line break so tokens appear on different lines
          appendToLastMessage?.(`\n\n${chunk}`);
        } catch {}
      });

      es.addEventListener("context_update", (ev) => {
        try {
          const data = JSON.parse((ev as MessageEvent).data as string);
          addDebugEvent(projectId, {
            id: `${Date.now()}-context_update`,
            sessionId: sessId,
            name: "context_update",
            timestamp: new Date().toISOString(),
            payload: data,
          });
        } catch {}
      });

      //TODO: I've added this to show "new" badge on the topic card but it's not working yet.
      // I'm not sure if this is the right place to do this.
      const handleTopicEntry = (ev: MessageEvent) => {
        try {
          const payload = JSON.parse(ev.data as string);
          addDebugEvent(projectId, {
            id: `${Date.now()}-topic_entry`,
            sessionId: sessId,
            name: (ev as any).type || "topic_entry",
            timestamp: new Date().toISOString(),
            payload,
          });
          // Supported payload shapes:
          // { sectionId, topicId, entries: BusinessItemDetail[] }
          // { topicId, entries: BusinessItemDetail[] }  // sectionId resolved via lookup
          // { sectionId, topicId, entry: BusinessItemDetail }
          // { topicId, entry: BusinessItemDetail }
          // Or an array of the above
          const handleOne = (obj: any) => {
            const sectionId =
              obj?.sectionId || obj?.section_id || obj?.section || undefined;
            const topicId =
              obj?.topicId || obj?.topic_id || obj?.topic || undefined;
            const entries: BusinessItemDetail[] | undefined = Array.isArray(
              obj?.entries
            )
              ? obj.entries
              : obj?.entry
              ? [obj.entry]
              : undefined;
            if (!topicId) return;
            if (entries && entries.length > 0) {
              if (sectionId) {
                addNewEntriesForTopic(
                  String(sectionId),
                  String(topicId),
                  entries
                );
              } else {
                addNewEntriesForTopicViaLookup(String(topicId), entries);
              }
            } else {
              // No entries provided, still set the new flag so UI dots appear
              markTopicHasNew(String(topicId));
            }
          };

          if (Array.isArray(payload)) {
            payload.forEach(handleOne);
          } else {
            handleOne(payload);
          }
        } catch {}
      };
      es.addEventListener("topic_entry", handleTopicEntry);
      es.addEventListener("topic_entries", handleTopicEntry);
      es.addEventListener("new_topic_entry", handleTopicEntry);
      es.addEventListener("topic_update", handleTopicEntry);

      const handleComplete = () => {
        console.log(
          `[useAiChat-${projectId}] Stream complete, stopping streaming state`
        );
        addDebugEvent(projectId, {
          id: `${Date.now()}-stream_complete`,
          sessionId: sessId,
          name: "stream_complete",
          timestamp: new Date().toISOString(),
          payload: { status: "stream_complete" },
        });
        setProjectStreaming(projectId, false);
        try {
          es.close();
        } catch {}
        if (esRef.current === es) esRef.current = null;
      };
      es.addEventListener("complete", handleComplete);
      es.addEventListener("stream_complete", handleComplete);

      // Additional completion signals: do not close ES, but unlock UI
      const handleTurnComplete = () => {
        addDebugEvent(projectId, {
          id: `${Date.now()}-chat_turn_complete`,
          sessionId: sessId,
          name: "chat_turn_complete",
          timestamp: new Date().toISOString(),
          payload: { status: "completed" },
        });
        setProjectStreaming(projectId, false);
      };
      es.addEventListener("chat_turn_complete", handleTurnComplete);
      es.addEventListener("coaching_complete", handleTurnComplete);

      // Suggestion lifecycle: ensure chat UI is not stuck in streaming
      es.addEventListener("suggestions_completed", (ev) => {
        try {
          const data = JSON.parse((ev as MessageEvent).data as string);
          addDebugEvent(projectId, {
            id: `${Date.now()}-suggestions_completed`,
            sessionId: sessId,
            name: "suggestions_completed",
            timestamp: new Date().toISOString(),
            payload: data,
          });
        } catch {}
        setProjectStreaming(projectId, false);
      });
      es.addEventListener("suggestions_error", (ev) => {
        try {
          const data = JSON.parse((ev as MessageEvent).data as string);
          addDebugEvent(projectId, {
            id: `${Date.now()}-suggestions_error`,
            sessionId: sessId,
            name: "suggestions_error",
            timestamp: new Date().toISOString(),
            payload: data,
          });
        } catch {}
        setProjectStreaming(projectId, false);
      });

      es.addEventListener("error", (ev) => {
        try {
          const data = JSON.parse((ev as MessageEvent).data as string);
          addDebugEvent(projectId, {
            id: `${Date.now()}-error`,
            sessionId: sessId,
            name: "error",
            timestamp: new Date().toISOString(),
            payload: data,
          });
        } catch {
          addDebugEvent(projectId, {
            id: `${Date.now()}-error`,
            sessionId: sessId,
            name: "error",
            timestamp: new Date().toISOString(),
            payload: { message: "stream error" },
          });
        }
        setProjectStreaming(projectId, false);
        try {
          es.close();
        } catch {}
        if (esRef.current === es) esRef.current = null;
      });
    },
    [appendToLastMessage, ensureAiMessage, projectId, setProjectStreaming]
  );

  const sendMessage = useCallback(
    async (message: string) => {
      const trimmed = message?.trim();
      if (!trimmed || !projectId) return;

      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      // Reset streaming dedupe guards for new turn
      processedEventsRef.current.clear();
      lastAppendedChunkRef.current = null;
      tokenStreamAccumulatedLengthRef.current = 0;

      // Add user message immediately
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        user: "You",
        avatar: "",
        message: trimmed,
        timestamp: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
        isCurrentUser: true,
      };

      addMessage(userMessage);
      setProjectLoading(projectId, true);

      try {
        // Ensure we have a real session id
        let effectiveSessionId: string | null = sessionId || null;
        const existingId = chatSession?.id;
        if (
          existingId &&
          existingId.length > 0 &&
          existingId !== `session-${projectId}`
        ) {
          effectiveSessionId = existingId;
        }

        if (!effectiveSessionId) {
          // Try discover active session for this project
          try {
            const discoverResp = await fetch(
              `/api/ai-chat/projects/${projectId}/sessions`,
              { credentials: "include" }
            );
            if (discoverResp.ok) {
              const root = await discoverResp.json();
              const list: Array<any> = Array.isArray(root)
                ? root
                : Array.isArray(root?.sessions)
                ? root.sessions
                : Array.isArray(root?.data)
                ? root.data
                : [];
              // Prefer latest active by updatedAt if available
              const actives = list.filter((s: any) => s?.status === "active");
              const sorted = actives.sort((a: any, b: any) => {
                const at = new Date(
                  a?.updatedAt || a?.updated_at || a?.createdAt || 0
                ).getTime();
                const bt = new Date(
                  b?.updatedAt || b?.updated_at || b?.createdAt || 0
                ).getTime();
                return bt - at;
              });
              const chosen = sorted[0] || list[0];
              if (chosen) {
                effectiveSessionId =
                  chosen.id || chosen.session_id || chosen.sessionId || null;
              }
            }
          } catch {}
        }

        if (!effectiveSessionId) {
          // Create a new session
          const createResp = await fetch("/api/ai-chat/sessions", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ projectId }),
            credentials: "include",
          });
          if (!createResp.ok) {
            throw new Error(
              `Failed to create AI session: ${createResp.status} ${createResp.statusText}`
            );
          }
          const created = await createResp.json();
          effectiveSessionId =
            created?.session_id ??
            created?.id ??
            created?.data?.id ??
            created?.sessionId ??
            null;
          if (!effectiveSessionId) {
            throw new Error("No session ID returned from session creation");
          }
        }

        // Persist session on hook and store
        if (effectiveSessionId) {
          setSessionId(effectiveSessionId);
          if (!chatSession || chatSession.id !== effectiveSessionId) {
            setChatSession({ id: effectiveSessionId, projectId });
          }
          try {
            window.localStorage.setItem(
              `chat_session_${projectId}`,
              effectiveSessionId
            );
          } catch {}
        }

        // Open SSE BEFORE sending chat so tokens have a stream to go to
        openStream(effectiveSessionId);
        // Switch to streaming state; SSE listeners will append tokens
        setProjectLoading(projectId, false);
        setProjectStreaming(projectId, true);
        // Ensure an AI message container exists to stream into with "Sifting..." placeholder
        updateLastMessage("Sifting...");

        // Build payload according to new API shape (stage + optional topic_name)
        const normalizeTopic = (name?: string | null) =>
          (name || "")
            .toString()
            .trim()
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, "")
            .replace(/\s+/g, "-")
            .replace(/-+/g, "-");
        const topic_name = options?.topicName
          ? normalizeTopic(options.topicName)
          : undefined;

        // Send coaching message to the session chat endpoint
        const resp = await fetch(
          `/api/ai-chat/sessions/${effectiveSessionId}/chat`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              content: trimmed,
              stage: "coaching",
              ...(topic_name ? { topic_name } : {}),
            }),
            credentials: "include",
            signal: abortControllerRef.current.signal,
          }
        );

        if (!resp.ok) {
          const errorText = await resp.text().catch(() => "");
          throw new Error(
            `Chat failed: ${resp.status} ${resp.statusText} ${errorText}`
          );
        }
        // Don't set streaming to false here - let the SSE events handle completion
      } catch (error: any) {
        console.error("[useAiChat] Error sending message:", error);
        if (error?.name !== "AbortError") {
          const errorMessage: ChatMessage = {
            id: `error-${Date.now()}`,
            user: "System",
            avatar: "",
            message:
              "Sorry, I'm having trouble responding right now. Please try again later.",
            timestamp: new Date().toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            }),
            isCurrentUser: false,
          };
          addMessage(errorMessage);
        }
        setProjectStreaming(projectId, false);
      } finally {
        setProjectLoading(projectId, false);
        abortControllerRef.current = null;
      }
    },
    [
      projectId,
      sessionId,
      addMessage,
      updateLastMessage,
      appendToLastMessage,
      setProjectLoading,
      setProjectStreaming,
      chatSession,
      setChatSession,
      openStream,
      options?.topicName,
    ]
  );

  // Cleanup SSE on unmount
  useEffect(() => {
    return () => {
      try {
        esRef.current?.close();
      } catch {}
      esRef.current = null;
    };
  }, []);

  const cancelMessage = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setProjectLoading(projectId, false);
      setProjectStreaming(projectId, false);
    }
  }, [projectId, setProjectLoading, setProjectStreaming]);

  return {
    sendMessage,
    cancelMessage,
    isLoading,
    isStreaming,
    sessionId,
  };
}
