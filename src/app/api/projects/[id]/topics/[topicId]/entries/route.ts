import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

const BACKEND_URL =
  process.env.NEXT_PUBLIC_ADMIN_API_URL ||
  process.env.NEXT_PUBLIC_API_URL ||
  "http://localhost:3002";
const STRICT_BACKEND = process.env.NEXT_PUBLIC_STRICT_BACKEND !== "false";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string; topicId: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id, topicId } = await params;
    try {
      console.log("[Entries API] GET proxy →", {
        projectId: id,
        topicId,
        BACKEND_URL,
      });
      const response = await fetch(
        `${BACKEND_URL}/api/projects/${id}/topics/${topicId}/entries`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json({ success: true, data });
      } else if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend error" },
          { status: response.status || 502 }
        );
      }
    } catch (err) {
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Fallback disabled" },
      { status: 502 }
    );
  } catch (error) {
    return NextResponse.json(
      { success: false, error: "Failed to fetch topic entries" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; topicId: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id, topicId } = await params;
    const url = new URL(request.url);
    const entryId = url.searchParams.get("entryId");
    if (!entryId) {
      return NextResponse.json(
        { success: false, error: "entryId is required" },
        { status: 400 }
      );
    }

    try {
      console.log("[Entries API] DELETE proxy →", {
        projectId: id,
        topicId,
        entryId,
        BACKEND_URL,
      });
      const response = await fetch(
        `${BACKEND_URL}/api/projects/${id}/topics/${topicId}/entries/${entryId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      console.log(
        "[Entries API] DELETE backend status:",
        response.status,
        response.statusText
      );
      if (response.ok) {
        return NextResponse.json({ success: true }, { status: 200 });
      } else if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend error" },
          { status: response.status || 502 }
        );
      }
    } catch (err) {
      console.error("[Entries API] DELETE error", err);
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Fallback disabled" },
      { status: 502 }
    );
  } catch (error) {
    return NextResponse.json(
      { success: false, error: "Failed to delete topic entry" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; topicId: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id, topicId } = await params;
    const url = new URL(request.url);
    const entryIdFromQuery = url.searchParams.get("entryId");
    const body = await request
      .json()
      .catch(() => ({} as Record<string, unknown>));
    const entryIdFromBody =
      body && (body as any).id ? String((body as any).id) : null;
    const entryId = entryIdFromQuery || entryIdFromBody;

    const backendUrl = entryId
      ? `${BACKEND_URL}/api/projects/${id}/topics/${topicId}/entries/${entryId}`
      : `${BACKEND_URL}/api/projects/${id}/topics/${topicId}/entries`;

    // Remove local id from payload if present; backend takes it from path
    if (body && (body as any).id) {
      delete (body as any).id;
    }

    try {
      console.log("[Entries API] PATCH proxy →", {
        projectId: id,
        topicId,
        entryId,
        backendUrl,
        body,
      });
      const response = await fetch(backendUrl, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body || {}),
      });
      const data = await response.json().catch(() => ({}));
      console.log(
        "[Entries API] PATCH backend status:",
        response.status,
        response.statusText
      );
      console.log(
        "[Entries API] PATCH backend response data:",
        JSON.stringify(data, null, 2)
      );
      if (response.ok) {
        return NextResponse.json({ success: true, data }, { status: 200 });
      } else if (STRICT_BACKEND) {
        const errorMessage =
          data?.message ||
          data?.error ||
          `Backend error: ${response.status} ${response.statusText}`;
        console.error("[Entries API] PATCH backend error:", errorMessage);
        return NextResponse.json(
          { success: false, error: errorMessage },
          { status: response.status || 502 }
        );
      }
    } catch (err) {
      console.error("[Entries API] PATCH error", err);
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Fallback disabled" },
      { status: 502 }
    );
  } catch (error) {
    return NextResponse.json(
      { success: false, error: "Failed to update topic entries" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; topicId: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id, topicId } = await params;
    const body = await request.json().catch(() => ({}));

    try {
      console.log("[Entries API] POST proxy →", {
        projectId: id,
        topicId,
        BACKEND_URL,
        body,
      });
      const response = await fetch(
        `${BACKEND_URL}/api/projects/${id}/topics/${topicId}/entries`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(body),
        }
      );

      const data = await response.json().catch(() => ({}));
      console.log(
        "[Entries API] POST backend status:",
        response.status,
        response.statusText
      );
      console.log(
        "[Entries API] POST backend response data:",
        JSON.stringify(data, null, 2)
      );
      if (response.ok) {
        return NextResponse.json({ success: true, data }, { status: 201 });
      } else if (STRICT_BACKEND) {
        const errorMessage =
          data?.message ||
          data?.error ||
          `Backend error: ${response.status} ${response.statusText}`;
        console.error("[Entries API] POST backend error:", errorMessage);
        return NextResponse.json(
          { success: false, error: errorMessage },
          { status: response.status || 502 }
        );
      }
    } catch (err) {
      console.error("[Entries API] POST error", err);
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Fallback disabled" },
      { status: 502 }
    );
  } catch (error) {
    return NextResponse.json(
      { success: false, error: "Failed to create topic entry" },
      { status: 500 }
    );
  }
}
