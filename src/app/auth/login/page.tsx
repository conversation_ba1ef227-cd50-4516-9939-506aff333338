"use client";

// Prevent static generation for this page
export const dynamic = "force-dynamic";

import { useSignIn } from "@clerk/nextjs";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ArrowLeft,
  Eye,
  EyeOff,
  Loader2,
  Mail,
  Lock,
  Home,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { AuthCard } from "@/components/auth/auth-card";
import { BetaAccessGate } from "@/components/landing/BetaAccessGate";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { useSyncUserToBackend } from "@/hooks/mutations/useUserMutations";
import { IButton } from "../../../components/ui/ibutton";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import { DottedBackground } from "../../../components/ui/dotted-background";

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  // Always call hooks in a stable order (before any conditional returns)
  const { isLoaded, signIn, setActive } = useSignIn();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const syncUser = useSyncUserToBackend();
  const { isSignedIn, isLoaded: authLoaded } = useClerkAuth();

  // Beta gate state
  const [hasBeta, setHasBeta] = useState<boolean>(false);

  // Check beta cookie client-side
  useEffect(() => {
    const cookies = document.cookie.split("; ");
    const betaCookie = cookies.find((c) => c.startsWith("beta_access="));
    const betaValue = betaCookie?.split("=")[1];
    setHasBeta(betaValue === "1");
  }, []);

  // If already authenticated AND beta access present, redirect to workspace/dashboard
  useEffect(() => {
    if (authLoaded && isSignedIn && hasBeta) {
      const nextParam = searchParams?.get("next");
      if (nextParam) {
        router.replace(nextParam);
      } else {
        router.replace("/user-dashboard");
      }
    }
  }, [authLoaded, isSignedIn, hasBeta, router, searchParams]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  // Avoid flicker while redirecting authenticated users (only when beta is present)
  if (authLoaded && isSignedIn && hasBeta) {
    return null;
  }

  if (!hasBeta) {
    return <BetaAccessGate mode="auth" onUnlock={() => setHasBeta(true)} />;
  }

  const onSubmit = async (data: LoginFormData) => {
    if (!isLoaded) return;

    setIsLoading(true);
    try {
      const result = await signIn.create({
        identifier: data.email,
        password: data.password,
      });

      if (result.status === "complete") {
        await setActive({ session: result.createdSessionId });

        // Ensure user is synced to backend (in case they weren't before)
        try {
          console.log("Checking/syncing user to backend...");
          await syncUser.mutateAsync(undefined);
          console.log("User sync check completed");
        } catch (syncError) {
          console.warn(
            "User sync check failed (user might already exist):",
            syncError
          );
          // Don't fail login if sync fails - user might already exist
        }

        toast.success("Welcome back!");
        const nextParam = searchParams?.get("next");
        router.push(nextParam || "/user-dashboard");
      } else {
        console.error("Sign in not complete:", result);
        toast.error("Sign in failed. Please try again.");
      }
    } catch (err: any) {
      console.error("Sign in error:", err);

      let userMessage = "Sign in failed. Please try again.";

      if (err.errors && err.errors.length > 0) {
        const error = err.errors[0];

        switch (error.code) {
          case "form_identifier_not_found":
            userMessage =
              "No account found with this email. Please check your email or sign up.";
            break;
          case "form_password_incorrect":
            userMessage =
              "Incorrect password. Please try again or reset your password.";
            break;
          case "form_identifier_exists":
            userMessage =
              "Please complete your account setup or try signing in.";
            break;
          case "session_exists":
            userMessage = "You're already signed in. Redirecting...";
            // Redirect to dashboard if already signed in
            setTimeout(() => router.push("/user-dashboard"), 1000);
            break;
          case "too_many_requests":
            userMessage =
              "Too many sign in attempts. Please wait a few minutes and try again.";
            break;
          default:
            if (error.message) {
              userMessage = error.message;
            }
        }
      }

      toast.error(userMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (strategy: "oauth_google") => {
    if (!isLoaded) return;

    try {
      const nextParam = searchParams?.get("next") || "/user-dashboard";
      await signIn.authenticateWithRedirect({
        strategy,
        redirectUrl: "/sso-callback",
        redirectUrlComplete: nextParam,
      });
    } catch (err: any) {
      console.error("Social sign in error:", err);

      let userMessage = "Social sign in failed. Please try again.";

      if (err.errors && err.errors.length > 0) {
        const error = err.errors[0];

        switch (error.code) {
          case "oauth_access_denied":
            userMessage =
              "Access was denied. Please try again and allow the necessary permissions.";
            break;
          case "oauth_email_domain_reserved_by_saml":
            userMessage =
              "This email domain requires SAML authentication. Please contact your administrator.";
            break;
          case "external_account_exists":
            userMessage =
              "An account with this email already exists. Please try signing in instead.";
            break;
          default:
            if (error.message) {
              userMessage = error.message;
            }
        }
      }

      toast.error(userMessage);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center relative overflow-hidden">
      <DottedBackground
        fadeEdge={20}
        dotSizes={[1, 1.5, 2]}
        spacing={25}
        dotsPerRow={8}
        opacity={0.01}
        darkOpacity={0.15}
        className="pointer-events-none bg-[var(--siift-light-accent)]/30"
      />

      <AuthCard
        title="Welcome back"
        description="Sign in to your account to continue"
        footer={
          <div className="text-center text-sm text-muted-foreground">
            Don't have an account?{" "}
            <Link
              href="/auth/register"
              className="text-primary hover:text-primary/90 font-medium"
            >
              Sign up
            </Link>
          </div>
        }
      >
        {/* Social Sign In Buttons */}
        <div className="w-full ">
          <IButton
            type="button"
            layout="horizontal"
            hoverColor="green"
            hoverScale={true}
            showBorder={true}
            borderClassName="grey border-1"
            variant="outline"
            className="w-full"
            onClick={() => handleSocialSignIn("oauth_google")}
            disabled={!isLoaded}
          >
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continue with Google
          </IButton>
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <Separator className="w-full" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Or continue with
            </span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email address</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                {...register("email")}
                className={`pl-10 ${errors.email ? "border-destructive" : ""}`}
              />
            </div>
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter your password"
                {...register("password")}
                className={`pl-10 pr-10 ${
                  errors.password ? "border-destructive" : ""
                }`}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1.5 h-4 w-4 text-muted-foreground hover:text-foreground"
              >
                {showPassword ? <EyeOff /> : <Eye />}
              </button>
            </div>
            {errors.password && (
              <p className="text-sm text-destructive">
                {errors.password.message}
              </p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <Link
              href="/auth/forgot-password"
              className="text-sm text-primary hover:text-primary/90"
            >
              Forgot password?
            </Link>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || !isLoaded}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Signing in...
              </>
            ) : (
              "Sign in"
            )}
          </Button>
        </form>
      </AuthCard>

      {/* Home link - Outside the card */}
      <div className="text-center mt-6">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <Home className="w-4 h-4" />
          Back to home
        </Link>
      </div>
    </div>
  );
}
